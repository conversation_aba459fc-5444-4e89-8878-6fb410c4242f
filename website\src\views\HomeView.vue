<script setup lang="ts">
import { ref, onMounted, computed, onUnmounted } from 'vue'
import HomeBanner from '../components/HomeBanner.vue'
import DramaTag from '../components/common/DramaTag.vue'
import { getPublicDramas, getPublicPlatforms, type Platform } from '../api/dramaService'
import { getWebsiteStats, type WebsiteStats } from '../api/websiteAPI'
import { useTags } from '../composables/useTags'
import { useRouter } from 'vue-router'
import type { Drama } from '../types'

const router = useRouter()

// 标签管理
const { loadTags, tagMap, parseTagData } = useTags()



// 工具提示当前活跃ID
const activeTooltip = ref(null)

// 显示工具提示
const showTooltip = (id: any) => {
  activeTooltip.value = id
}

// 隐藏工具提示
const hideTooltip = () => {
  activeTooltip.value = null
}



// 数据状态
const dramas = ref<Drama[]>([])
const loading = ref(false)
const error = ref<string | null>(null)

// 网站统计数据
const websiteStats = ref<WebsiteStats | null>(null)
const statsLoading = ref(false)
const statsError = ref<string | null>(null)



// 项目专管员联系弹窗状态
const showContactModal = ref(false)

// 实时投资人数（在1575-1685之间随机变化）
const realtimeInvestors = ref(1575)
const currentUserIndex = ref(0)

// 定时器引用
let investorTimer: NodeJS.Timeout | null = null
let userTimer: NodeJS.Timeout | null = null

const highlights = ref([
  {
    id: 1,
    title: "丰富项目资源",
    description: "汇聚优质短剧IP项目库，精选高潜力影视作品，多元化投资选择降低风险",
    roi: "500+",
    icon: 'chart-pie',
    color: 'bg-blue-500'
  },
  {
    id: 2,
    title: "专业风险控制",
    description: "完善的项目评估体系，资深团队严格把关，多重保障机制确保投资安全",
    roi: "AAA",
    icon: 'shield-check',
    color: 'bg-green-500'
  },
  {
    id: 3,
    title: "透明收益分配",
    description: "公开透明的收益分配机制，定期结算投资回报，让每一分收益都清晰可见",
    roi: "100%",
    icon: 'trending-up',
    color: 'bg-purple-500'
  },
  {
    id: 4,
    title: "资深团队支持",
    description: "行业资深专家团队，丰富的影视投资经验，全程专业指导和服务支持",
    roi: "10年+",
    icon: 'users',
    color: 'bg-orange-500'
  }
])

// 募资人说数据
const fundraiserReviews = ref([
  {
    id: 1,
    name: "张总",
    company: "星光影视CEO",
    review: "平台募资效率非常高，我们的项目在3天内就完成了目标金额的80%，投资人对接速度很快，服务团队专业贴心。",
    rating: 4.8
  },
  {
    id: 2,
    name: "李导",
    company: "梦想工作室创始人",
    review: "通过剧投投平台，我们成功募集到500万资金，整个流程透明高效，平台的推广支持让我们的项目获得了更多关注。",
    rating: 4.9
  },
  {
    id: 3,
    name: "王制片",
    company: "华彩传媒制片人",
    review: "募资频次高，资金到位快，平台的专业服务让我们专注于内容创作，不用担心资金问题，强烈推荐给同行。",
    rating: 5.0
  },
  {
    id: 4,
    name: "陈总监",
    company: "青春影业投资总监",
    review: "平台的投资人质量很高，都是真正懂短剧行业的专业投资者，沟通效率高，决策速度快，合作非常愉快。",
    rating: 4.7
  },
  {
    id: 5,
    name: "刘编剧",
    company: "原创工坊编剧",
    review: "作为独立编剧，通过平台找到了合适的投资方，募资金额超出预期，平台的服务团队给了我们很多专业建议。",
    rating: 4.6
  },
  {
    id: 6,
    name: "赵总",
    company: "新锐传媒CEO",
    review: "平台的募资速度让人惊喜，我们的项目在一周内就达到了募资目标，投资人的专业度和积极性都很高。",
    rating: 4.8
  },
  {
    id: 7,
    name: "孙导演",
    company: "独立导演",
    review: "第一次使用就被平台的专业服务震撼了，从项目包装到投资人对接，每个环节都有专人跟进，效率极高。",
    rating: 4.9
  },
  {
    id: 8,
    name: "周制片",
    company: "飞扬影视制片人",
    review: "平台的投资人资源丰富，我们的项目得到了多家投资机构的关注，最终选择了最合适的合作伙伴。",
    rating: 4.5
  },
  {
    id: 9,
    name: "吴总",
    company: "创意无限CEO",
    review: "募资流程简单透明，资金管理规范，平台的风控体系让投资人更有信心，我们的募资成功率大大提升。",
    rating: 4.7
  },
  {
    id: 10,
    name: "马导",
    company: "新生代导演",
    review: "平台不仅帮我们募集到了资金，还提供了很多行业资源和专业指导，真正做到了全方位的服务支持。",
    rating: 5.0
  },
  {
    id: 11,
    name: "胡总",
    company: "光影传媒CEO",
    review: "在剧投投平台上，我们的古装短剧项目仅用5天就完成了800万的募资目标，投资人的响应速度超出预期。",
    rating: 4.8
  },
  {
    id: 12,
    name: "林制片",
    company: "青春制作制片人",
    review: "平台的专业度让我印象深刻，从项目评估到资金到位，每个环节都有详细的进度反馈，让我们很安心。",
    rating: 4.6
  },
  {
    id: 13,
    name: "徐导",
    company: "新锐导演",
    review: "作为新人导演，平台给了我很多机会，不仅帮助募资成功，还介绍了很多行业资源，对我的职业发展帮助很大。",
    rating: 4.9
  },
  {
    id: 14,
    name: "何总监",
    company: "华夏影业投资总监",
    review: "平台的风控体系很完善，投资人对我们的项目很有信心，募资过程中没有遇到任何资金安全问题。",
    rating: 4.7
  },
  {
    id: 15,
    name: "郭编剧",
    company: "金牌编剧工作室",
    review: "通过平台认识了很多志同道合的投资人，他们不仅提供资金支持，还给出了很多创作上的建议。",
    rating: 4.5
  },
  {
    id: 16,
    name: "邓总",
    company: "星辰传媒CEO",
    review: "平台的服务效率真的很高，我们的悬疑短剧项目从提交到完成募资只用了一周时间，非常满意。",
    rating: 5.0
  },
  {
    id: 17,
    name: "韩制片",
    company: "梦幻影视制片人",
    review: "募资金额达到了我们的预期目标，平台的推广策略很有效，让我们的项目获得了更多曝光。",
    rating: 4.8
  },
  {
    id: 18,
    name: "冯导",
    company: "独立制片人",
    review: "平台的投资人都很专业，对短剧行业有深入了解，合作过程中给了我们很多宝贵的意见。",
    rating: 4.6
  },
  {
    id: 19,
    name: "曾总",
    company: "飞跃影业CEO",
    review: "第二次在平台募资了，依然很满意。平台的服务质量稳定，投资人资源丰富，值得信赖。",
    rating: 4.9
  },
  {
    id: 20,
    name: "谭制片",
    company: "新时代制作制片人",
    review: "平台的数据分析很专业，帮我们优化了项目方案，最终募资金额比预期高出了30%。",
    rating: 4.7
  },
  {
    id: 21,
    name: "石导",
    company: "青年导演",
    review: "作为年轻导演，平台给了我展示才华的机会，投资人对我的项目很认可，这让我很有成就感。",
    rating: 4.4
  },
  {
    id: 22,
    name: "廖总监",
    company: "盛世传媒投资总监",
    review: "平台的项目匹配度很高，推荐给我们的投资人都很符合我们的需求，节省了大量的沟通成本。",
    rating: 4.8
  },
  {
    id: 23,
    name: "姚编剧",
    company: "原创剧本工作室",
    review: "通过平台不仅完成了募资，还学到了很多行业知识，平台的培训和指导服务很有价值。",
    rating: 4.6
  },
  {
    id: 24,
    name: "贺总",
    company: "光速影视CEO",
    review: "平台的募资速度真的很快，我们的都市情感短剧项目在48小时内就有投资人联系，效率惊人。",
    rating: 4.3
  }
])

// 将评价数据分配到两行，实现无缝循环滚动
const getFirstRowReviews = () => {
  // 复制数据多次以实现无缝循环
  const reviews = [...fundraiserReviews.value, ...fundraiserReviews.value, ...fundraiserReviews.value]
  return reviews
}

const getSecondRowReviews = () => {
  // 从第二个评价开始，错开显示，复制数据多次以实现无缝循环
  const reviews = [...fundraiserReviews.value.slice(1), ...fundraiserReviews.value, ...fundraiserReviews.value, ...fundraiserReviews.value.slice(0, 1)]
  return reviews
}

// 根据评分生成星星显示状态
const getStarDisplay = (rating: number) => {
  const fullStars = Math.floor(rating)
  const hasHalfStar = rating % 1 >= 0.5
  const stars = []

  // 添加满星
  for (let i = 0; i < fullStars; i++) {
    stars.push('full')
  }

  // 添加半星
  if (hasHalfStar && fullStars < 5) {
    stars.push('half')
  }

  // 添加空星
  while (stars.length < 5) {
    stars.push('empty')
  }

  return stars
}

const investmentProcess = ref([
  {
    id: 1,
    title: "项目提交审核",
    description: "提交短剧项目资料",
    details: "提交完整的项目策划书、剧本大纲、制作团队信息等资料，平台专业团队进行项目评估和审核",
    icon: "document-text",
    color: "bg-blue-500"
  },
  {
    id: 2,
    title: "募资启动推广",
    description: "项目上线开始募资",
    details: "审核通过后项目正式上线，平台提供专业推广支持，帮助项目快速获得投资人关注",
    icon: "search",
    color: "bg-green-500"
  },
  {
    id: 3,
    title: "资金管理分配",
    description: "募资资金安全托管",
    details: "募集资金由银行第三方托管，按制作进度分批拨付，确保资金安全和合理使用",
    icon: "cash",
    color: "bg-yellow-500"
  },
  {
    id: 4,
    title: "项目制作回报",
    description: "制作完成收益分配",
    details: "项目制作完成上线后，根据实际收益情况进行透明的收益分配，制作方获得相应回报",
    icon: "film",
    color: "bg-purple-500"
  }
])

// 短剧平台数据
const platforms = ref<Platform[]>([])
const platformsLoading = ref(false)
const platformsError = ref('')

// 加载短剧平台数据
const loadPlatforms = async () => {
  try {
    platformsLoading.value = true
    platformsError.value = ''
    const response = await getPublicPlatforms()
    if (response.data && response.data.success) {
      platforms.value = response.data.data
    } else {
      platformsError.value = response.data?.message || '获取平台数据失败'
    }
  } catch (error) {
    console.error('加载平台数据失败:', error)
    platformsError.value = '网络错误，请稍后重试'
  } finally {
    platformsLoading.value = false
  }
}





const newsCategories = ref(["全部", "行业动态", "投资资讯", "项目更新"])
const activeNewsCategory = ref("全部")

// 行业快讯数据
const newsData = ref([
  {
    id: 1,
    title: "短剧市场迎来爆发式增长，2024年预计突破500亿规模",
    summary: "据最新行业报告显示，短剧市场在2024年预计将突破500亿元规模，同比增长超过150%。微短剧作为新兴内容形态，正在重塑整个影视娱乐产业格局。",
    category: "行业动态",
    author: "剧投投研究院",
    publishDate: "2024-08-15",
    readCount: 15420,
    coverImage: "https://via.placeholder.com/400x240/3B82F6/FFFFFF?text=短剧市场分析",
    content: `
# 短剧市场迎来爆发式增长，2024年预计突破500亿规模

## 市场概况

据剧投投研究院最新发布的《2024年中国短剧市场发展报告》显示，短剧市场正在经历前所未有的爆发式增长。预计2024年整体市场规模将突破500亿元人民币，相比2023年的200亿元规模，同比增长率将超过150%。

这一增长数据不仅反映了短剧内容的受众接受度快速提升，更体现了整个产业链的日趋成熟。从内容制作、平台分发到商业变现，短剧产业已经形成了相对完整的生态体系。

## 增长驱动因素

### 1. 用户消费习惯的转变

随着移动互联网的深度普及和5G技术的广泛应用，用户的内容消费习惯正在发生根本性转变。短剧以其"短平快"的特点，完美契合了现代人碎片化的时间使用习惯。

数据显示，短剧的平均单集时长在1-3分钟之间，这种时长设计既能保证故事的完整性，又能最大化利用用户的碎片时间。相比传统长剧动辄40-60分钟的单集时长，短剧在用户时间争夺战中具有明显优势。

### 2. 制作成本的优化

短剧制作相比传统影视剧具有明显的成本优势。一部优质短剧的制作成本通常在50万-200万元之间，而传统电视剧的制作成本往往需要数千万甚至上亿元。

这种成本优势使得更多的创作者和投资方能够参与到内容制作中来，极大地丰富了市场供给。同时，较低的制作门槛也为创新内容的涌现提供了土壤。

### 3. 商业模式的创新

短剧行业在商业模式上展现出了强大的创新能力。除了传统的广告收入模式外，付费观看、会员订阅、IP衍生品开发等多元化变现方式正在快速发展。

特别是付费观看模式，已经成为短剧平台的重要收入来源。数据显示，愿意为优质短剧内容付费的用户比例已经超过40%，这一比例还在持续上升。

## 平台竞争格局

### 主流平台表现

目前，短剧市场的主要玩家包括抖音、快手、微博、B站等传统短视频平台，以及专门的短剧平台如剧投投、微短剧等。各平台都在加大对短剧内容的投入力度。

抖音作为短视频领域的头部平台，在短剧领域也展现出了强劲的竞争力。其"抖音短剧"频道已经聚集了大量优质内容，日活跃用户数超过5000万。

快手则通过"快手小剧场"等产品，在下沉市场建立了稳固的用户基础。其短剧内容更加贴近三四线城市用户的喜好，形成了差异化竞争优势。

### 专业平台的崛起

除了传统短视频平台外，专门的短剧平台也在快速崛起。剧投投作为专业的短剧投资与制作平台，通过"投资+制作+分发"的全产业链模式，为行业提供了新的发展思路。

这些专业平台的优势在于对短剧内容的深度理解和专业化运营能力。它们不仅能够为创作者提供更好的创作环境，还能为投资者提供更加透明和专业的投资服务。

## 内容趋势分析

### 题材多元化

短剧的题材正在呈现多元化发展趋势。从最初的都市情感、霸道总裁等传统题材，逐步扩展到悬疑推理、科幻奇幻、历史古装等多个领域。

这种题材的多元化不仅满足了不同用户群体的需求，也为创作者提供了更大的创作空间。同时，垂直化的内容定位也有助于平台建立更加精准的用户画像。

### 制作水准提升

随着市场竞争的加剧，短剧的制作水准正在快速提升。从剧本创作、演员选择到后期制作，各个环节都在向专业化、精品化方向发展。

许多短剧作品在视觉效果、音响效果等技术层面已经达到了电影级别的水准。这种制作水准的提升不仅提高了用户的观看体验，也为短剧行业赢得了更多的社会认可。

## 投资机会与挑战

### 投资机会

短剧市场的快速发展为投资者提供了丰富的投资机会。从内容制作、技术服务到平台运营，产业链的各个环节都存在投资价值。

特别是在内容制作领域，优质IP的稀缺性使得具有创新能力的制作团队成为投资热点。同时，技术服务商也在这一轮发展中获得了大量机会，包括视频制作工具、数据分析服务、营销推广等。

### 面临挑战

尽管市场前景广阔，但短剧行业也面临着一些挑战。首先是内容同质化问题，随着入局者的增多，内容创新的难度在不断加大。

其次是监管政策的不确定性。作为新兴业态，短剧行业的监管框架还在不断完善中，政策变化可能对行业发展产生重要影响。

最后是人才短缺问题。短剧行业需要既懂传统影视制作又了解互联网运营的复合型人才，这类人才目前还比较稀缺。

## 未来展望

展望未来，短剧市场仍将保持高速增长态势。预计到2025年，市场规模有望突破800亿元。同时，随着技术的不断进步和商业模式的持续创新，短剧行业将迎来更加广阔的发展空间。

对于投资者而言，现在正是布局短剧市场的最佳时机。通过选择优质的项目和团队，投资者不仅能够获得可观的财务回报，还能参与到这一新兴产业的发展进程中来。

剧投投作为行业领先的短剧投资平台，将继续为投资者和创作者搭建桥梁，推动整个行业的健康发展。我们相信，在各方的共同努力下，短剧行业必将迎来更加辉煌的明天。
    `
  },
  {
    id: 2,
    title: "政策利好频出，短剧行业迎来规范化发展新机遇",
    summary: "国家广电总局近期发布多项政策文件，为短剧行业发展提供了明确的政策指引。新政策在内容审核、版权保护、商业模式等方面都有重要突破。",
    category: "政策法规",
    author: "政策解读小组",
    publishDate: "2024-08-12",
    readCount: 12350,
    coverImage: "https://via.placeholder.com/400x240/10B981/FFFFFF?text=政策解读",
    content: `
# 政策利好频出，短剧行业迎来规范化发展新机遇

## 政策背景

2024年8月，国家广播电视总局连续发布了《关于进一步规范网络微短剧创作生产的通知》、《网络视听节目内容审核通则（2024版）》等重要政策文件，为短剧行业的规范化发展指明了方向。

这些政策的出台，标志着监管部门对短剧这一新兴业态的重视程度不断提升，同时也为行业的健康发展提供了重要保障。对于从业者和投资者而言，理解和把握这些政策变化，将是未来成功的关键。

## 主要政策内容解读

### 1. 内容审核标准明确化

新版审核通则对短剧内容的审核标准进行了明确规定，主要包括以下几个方面：

**价值导向要求**：短剧内容必须坚持正确的价值导向，传播正能量，弘扬社会主义核心价值观。这一要求虽然看似严格，但实际上为优质内容的创作提供了明确的方向指引。

**题材限制规范**：对于涉及历史、军事、医疗等敏感题材的短剧，审核标准更加严格。创作者需要确保内容的真实性和准确性，避免误导观众。

**技术标准提升**：在视频质量、音频效果等技术层面，新政策也提出了更高要求。这将推动整个行业的制作水准向专业化方向发展。

### 2. 版权保护机制完善

新政策在版权保护方面有了重大突破，主要体现在：

**原创内容保护**：建立了更加完善的原创内容保护机制，对于抄袭、洗稿等行为将面临更严厉的处罚。这为原创内容创作者提供了更好的保护。

**IP开发规范**：对于基于小说、漫画等IP改编的短剧，明确了版权授权的具体要求和流程。这将有助于减少版权纠纷，促进IP产业的健康发展。

**平台责任明确**：各大平台需要建立完善的版权保护机制，对于侵权内容要及时处理。这将推动平台方加大对版权保护的投入。

### 3. 商业模式规范化

在商业模式方面，新政策也提出了明确要求：

**付费模式规范**：对于付费观看模式，要求平台必须明确告知用户付费规则，不得进行误导性宣传。同时，要保障用户的知情权和选择权。

**广告投放标准**：对于短剧中的广告植入，要求必须明确标识，不得影响用户的正常观看体验。这将推动广告投放的规范化发展。

**数据安全要求**：平台需要建立完善的用户数据保护机制，确保用户隐私安全。这对于平台的技术能力提出了更高要求。

## 政策影响分析

### 对创作者的影响

**积极影响**：
- 明确的审核标准有助于创作者更好地把握内容方向
- 版权保护机制的完善为原创内容提供了更好的保障
- 规范化的市场环境有利于优质内容的脱颖而出

**挑战与应对**：
- 审核标准的提高可能会增加内容创作的难度
- 创作者需要更加注重内容的质量和价值导向
- 建议创作者加强对政策的学习和理解，确保内容合规

### 对平台方的影响

**合规成本上升**：
新政策要求平台建立更加完善的内容审核、版权保护、用户数据保护等机制，这将导致平台的合规成本上升。

**竞争格局变化**：
具有更强技术实力和合规能力的平台将在竞争中占据优势，而一些小平台可能面临更大的生存压力。

**商业模式优化**：
平台需要在政策框架内优化商业模式，寻找新的增长点和盈利模式。

### 对投资者的影响

**投资门槛提高**：
随着行业规范化程度的提升，投资门槛也相应提高。投资者需要更加关注项目的合规性和可持续性。

**投资机会优化**：
政策的明确化为投资者提供了更加清晰的投资方向，有助于降低投资风险。

**长期价值凸显**：
规范化的发展环境有利于行业的长期健康发展，为投资者创造更大的长期价值。

## 行业应对策略

### 内容制作方面

**加强内容策划**：
制作方需要在项目策划阶段就充分考虑政策要求，确保内容方向的正确性。

**提升制作水准**：
随着技术标准的提高，制作方需要加大对技术设备和人才的投入，提升整体制作水准。

**建立合规体系**：
建立完善的内容合规审查体系，确保每一部作品都能够通过审核。

### 平台运营方面

**技术升级**：
平台需要加大对技术系统的投入，建立更加智能化的内容审核、版权保护、用户管理系统。

**人才培养**：
加强对审核人员、技术人员的培训，提升团队的专业能力。

**合作共赢**：
与监管部门、行业协会等建立良好的合作关系，共同推动行业的健康发展。

### 投资策略调整

**项目筛选标准**：
投资者需要将合规性作为项目筛选的重要标准，优先投资具有良好合规记录的项目。

**风险管理**：
建立更加完善的风险管理体系，对政策风险进行充分评估和防范。

**长期布局**：
在政策明确的背景下，投资者可以进行更加长期的战略布局。

## 未来发展趋势

### 行业集中度提升

随着政策门槛的提高，行业集中度将进一步提升。具有更强实力的头部企业将占据更大的市场份额，而一些实力较弱的企业可能面临淘汰。

### 内容质量持续提升

在政策引导下，整个行业将更加注重内容质量的提升。低质量、同质化的内容将逐步被市场淘汰，优质原创内容将获得更大的发展空间。

### 商业模式创新

在政策框架内，行业将探索更多创新的商业模式。除了传统的广告和付费模式外，IP衍生品开发、线下活动等新模式也将得到发展。

### 国际化发展

随着国内市场的规范化发展，优质的短剧内容也将加速走向国际市场，为中国文化的海外传播贡献力量。

## 结语

政策的出台虽然在短期内可能会给行业带来一定的调整压力，但从长期来看，这些政策为短剧行业的健康发展奠定了坚实基础。

对于从业者而言，关键是要积极拥抱政策变化，在合规的前提下寻找发展机遇。对于投资者而言，政策的明确化降低了投资风险，为长期投资提供了更好的环境。

剧投投将继续密切关注政策动态，为平台上的创作者和投资者提供最新的政策解读和合规指导，共同推动短剧行业的规范化、专业化发展。

我们相信，在政策的正确引导下，短剧行业必将迎来更加光明的未来，为广大观众提供更多优质的内容，为投资者创造更大的价值。
    `
  },
  {
    id: 3,
    title: "剧投投平台Q2投资报告：短剧项目平均回报率达180%",
    summary: "剧投投平台发布2024年第二季度投资报告，平台上短剧项目平均投资回报率达到180%，其中《都市情缘》系列项目回报率更是高达350%。",
    category: "投资资讯",
    author: "剧投投数据中心",
    publishDate: "2024-08-10",
    readCount: 18750,
    coverImage: "https://via.placeholder.com/400x240/8B5CF6/FFFFFF?text=投资报告",
    content: `
# 剧投投平台Q2投资报告：短剧项目平均回报率达180%

## 报告概述

剧投投平台今日发布《2024年第二季度短剧投资报告》，数据显示，平台上短剧项目的平均投资回报率达到180%，远超传统影视投资的平均水平。这一亮眼表现不仅体现了短剧市场的巨大潜力，也证明了剧投投平台在项目筛选和风险控制方面的专业能力。

报告期内，剧投投平台共完成投资项目52个，总投资金额达到3.2亿元，实现总收益5.76亿元。其中，《都市情缘》系列项目表现最为突出，投资回报率高达350%，成为平台历史上最成功的投资案例之一。

## 投资业绩分析

### 整体表现

**投资规模**：
- 总投资项目：52个
- 总投资金额：3.2亿元
- 平均单项投资：615万元
- 投资完成率：98.1%

**收益表现**：
- 总实现收益：5.76亿元
- 平均投资回报率：180%
- 最高单项回报率：350%（《都市情缘》系列）
- 盈利项目占比：94.2%

**风险控制**：
- 亏损项目：3个（占比5.8%）
- 平均亏损幅度：15%
- 风险控制有效率：95%以上

### 分类别表现

**按投资规模分类**：

*大型项目（投资额>1000万）*：
- 项目数量：8个
- 平均回报率：220%
- 代表项目：《都市情缘》、《霸道总裁的小娇妻》

*中型项目（投资额500-1000万）*：
- 项目数量：23个
- 平均回报率：175%
- 代表项目：《重生之商界女王》、《校园恋爱物语》

*小型项目（投资额<500万）*：
- 项目数量：21个
- 平均回报率：145%
- 代表项目：《青春校园》、《都市白领》

**按题材分类**：

*都市情感类*：
- 项目数量：18个
- 平均回报率：195%
- 市场接受度最高，用户付费意愿强

*古装言情类*：
- 项目数量：12个
- 平均回报率：165%
- 制作成本相对较高，但市场潜力巨大

*现代悬疑类*：
- 项目数量：10个
- 平均回报率：170%
- 新兴题材，增长潜力显著

*校园青春类*：
- 项目数量：8个
- 平均回报率：155%
- 目标用户群体明确，变现模式多样

*其他类型*：
- 项目数量：4个
- 平均回报率：140%
- 包括科幻、奇幻等创新题材

## 明星项目案例分析

### 《都市情缘》系列

**项目基本信息**：
- 投资金额：1200万元
- 制作周期：3个月
- 总集数：60集
- 单集时长：2-3分钟

**商业表现**：
- 总收益：5400万元
- 投资回报率：350%
- 播放量：15.8亿次
- 付费用户：280万人

**成功因素分析**：

*内容质量*：该项目在剧本创作、演员选择、后期制作等各个环节都保持了高水准，故事情节紧凑，人物形象鲜明，深受观众喜爱。

*营销策略*：采用了多平台联合推广的策略，通过社交媒体、短视频平台等渠道进行精准营销，有效提升了项目的知名度和影响力。

*商业模式*：创新性地采用了"免费观看+付费解锁"的模式，既保证了用户的观看体验，又实现了良好的商业变现。

*团队实力*：制作团队具有丰富的短剧制作经验，对市场需求有深刻理解，能够准确把握观众喜好。

### 《霸道总裁的小娇妻》

**项目基本信息**：
- 投资金额：800万元
- 制作周期：2.5个月
- 总集数：45集
- 单集时长：2分钟

**商业表现**：
- 总收益：2100万元
- 投资回报率：262%
- 播放量：12.3亿次
- 付费用户：195万人

**成功因素分析**：

*题材选择*：霸道总裁题材一直是短剧市场的热门选择，具有稳定的受众基础和较强的商业价值。

*制作效率*：项目在保证质量的前提下，有效控制了制作周期和成本，提高了投资效率。

*平台优势*：充分利用了剧投投平台的资源优势，在项目推广、用户获取等方面获得了有力支持。

## 市场趋势分析

### 用户行为变化

**付费意愿提升**：
数据显示，用户对优质短剧内容的付费意愿正在快速提升。Q2季度，平台用户的平均付费金额达到35元，相比Q1增长了40%。

**观看习惯优化**：
用户的观看习惯正在向更加理性的方向发展，对内容质量的要求不断提高，这为优质项目创造了更好的市场环境。

**社交传播增强**：
短剧内容的社交传播属性日益凸显，用户通过分享、讨论等方式参与到内容传播中，形成了良性的传播循环。

### 制作水准提升

**技术升级**：
短剧制作的技术水准正在快速提升，从拍摄设备、后期制作到音效配乐，各个环节都在向专业化方向发展。

**人才聚集**：
越来越多的专业影视人才开始关注短剧领域，为行业发展注入了新的活力。

**标准化进程**：
行业正在建立更加标准化的制作流程和质量标准，这有助于提高整体制作效率和质量稳定性。

### 商业模式创新

**多元化变现**：
除了传统的付费观看模式外，IP衍生品开发、线下活动、品牌合作等新的变现方式正在兴起。

**精准营销**：
基于大数据分析的精准营销模式正在成为主流，能够更有效地触达目标用户群体。

**生态化发展**：
短剧产业正在向生态化方向发展，形成了从内容创作、制作发行到商业变现的完整产业链。

## 风险因素分析

### 市场风险

**竞争加剧**：
随着市场的快速发展，竞争也在不断加剧。新入局者的增多可能会对现有项目的市场表现产生影响。

**用户需求变化**：
用户需求的快速变化要求制作方能够及时调整内容策略，否则可能面临市场风险。

**政策变化**：
监管政策的变化可能对行业发展产生重要影响，需要密切关注政策动向。

### 项目风险

**内容风险**：
内容质量的不稳定性是短剧投资面临的主要风险之一，需要建立完善的质量控制体系。

**制作风险**：
制作过程中可能出现的各种问题，如演员档期、技术故障等，都可能影响项目的按时完成。

**市场接受度风险**：
即使是高质量的内容，也可能因为市场接受度不高而影响商业表现。

### 应对策略

**多元化投资**：
通过投资不同类型、不同规模的项目，分散投资风险，提高整体投资组合的稳定性。

**专业化管理**：
建立专业的项目管理团队，从项目筛选、制作监督到市场推广，全程进行专业化管理。

**数据驱动决策**：
充分利用平台的数据优势，通过数据分析指导投资决策，提高投资成功率。

## 下半年展望

### 市场预期

基于Q2的优异表现和市场发展趋势，我们对下半年的市场表现保持乐观态度。预计下半年平台投资规模将达到5亿元，投资项目数量将超过80个。

### 投资策略

**重点布局**：
- 继续加大对都市情感类项目的投资力度
- 积极探索古装言情、现代悬疑等新兴题材
- 关注技术创新对内容制作的推动作用

**风险控制**：
- 进一步完善项目筛选标准
- 加强制作过程的监督管理
- 建立更加完善的风险预警机制

**生态建设**：
- 加强与优质制作团队的合作
- 完善平台服务体系
- 推动行业标准化发展

## 结语

Q2的优异表现证明了短剧投资的巨大潜力，也体现了剧投投平台的专业能力。面向未来，我们将继续坚持专业化、规范化的发展道路，为投资者创造更大的价值，为行业发展贡献更多力量。

我们相信，在各方的共同努力下，短剧行业必将迎来更加辉煌的发展前景，剧投投平台也将在这一进程中发挥更加重要的作用。

*注：本报告数据截至2024年6月30日，仅供参考。投资有风险，决策需谨慎。*
    `
  },
  {
    id: 4,
    title: "《重生之商界女王》项目完成拍摄，预计9月上线各大平台",
    summary: "剧投投平台重点投资项目《重生之商界女王》已完成全部拍摄工作，该项目总投资650万元，预计将于9月中旬在各大短剧平台同步上线。",
    category: "项目更新",
    author: "项目组",
    publishDate: "2024-08-08",
    readCount: 9680,
    coverImage: "https://via.placeholder.com/400x240/F59E0B/FFFFFF?text=项目更新"
  },
  {
    id: 5,
    title: "短剧出海正当时：中国短剧在东南亚市场表现亮眼",
    summary: "中国短剧正在加速出海步伐，在东南亚市场表现尤为突出。数据显示，中国短剧在泰国、越南、印尼等国的播放量同比增长超过300%。",
    category: "行业动态",
    author: "国际市场研究组",
    publishDate: "2024-08-05",
    readCount: 11200,
    coverImage: "https://via.placeholder.com/400x240/06B6D4/FFFFFF?text=出海报告"
  },
  {
    id: 6,
    title: "AI技术赋能短剧制作：成本降低40%，效率提升300%",
    summary: "人工智能技术正在深刻改变短剧制作流程，从剧本创作到后期制作，AI工具的应用让制作成本降低40%，制作效率提升300%。",
    category: "行业动态",
    author: "技术创新研究院",
    publishDate: "2024-08-02",
    readCount: 13580,
    coverImage: "https://via.placeholder.com/400x240/EF4444/FFFFFF?text=AI技术"
  }
])

// 筛选后的新闻数据
const filteredNews = computed(() => {
  if (activeNewsCategory.value === "全部") {
    return newsData.value
  }
  return newsData.value.filter(news => news.category === activeNewsCategory.value)
})

// 加载短剧数据

《重生之商界女王》总投资650万元，共48集，单集时长2-3分钟。项目预计将于9月中旬在抖音、快手、微博等主流短剧平台同步上线，同时也将在剧投投自有平台进行首发。

## 项目亮点

### 题材创新

**重生题材的新突破**：
《重生之商界女王》以女性创业为主线，讲述了一位商界精英意外重生后，利用前世经验在商场上叱咤风云的故事。这一题材既满足了观众对重生题材的喜爱，又融入了现代女性独立自强的价值观念。

**现实意义深刻**：
剧中展现的创业历程、商战智慧、职场生存法则等内容，不仅具有很强的娱乐性，也为观众提供了有价值的人生启示。这种寓教于乐的内容形式，正是当前短剧市场所需要的。

**人物设定丰富**：
女主角林雨萱的人物设定突破了传统短剧中女性角色的局限，她既有商界女强人的睿智果断，又保持了女性的温柔细腻。这种立体化的人物塑造为剧集增色不少。

### 制作水准

**专业团队打造**：
项目邀请了业内知名导演张明执导，编剧团队由多位资深编剧组成，确保了剧本的质量和故事的连贯性。摄影、灯光、音响等技术团队也都是行业内的佼佼者。

**演员阵容强大**：
女主角由新生代演员李小璐饰演，她在表演上的天赋和敬业精神得到了剧组的一致认可。男主角则由实力派演员王浩宇担纲，两人的化学反应为剧集增添了不少看点。

**制作标准严格**：
项目在制作过程中严格按照电影级别的标准执行，从服装道具到场景搭建，每一个细节都力求完美。后期制作更是投入了大量资源，确保视觉效果的精美。

### 商业价值

**市场定位精准**：
该项目主要面向25-40岁的都市女性群体，这一群体具有较强的消费能力和付费意愿，为项目的商业成功奠定了基础。

**变现模式多样**：
除了传统的付费观看模式外，项目还将开发周边产品、线下活动等多种变现方式。同时，剧中涉及的商业元素也为品牌植入提供了机会。

**IP开发潜力**：
《重生之商界女王》具有很强的IP开发潜力，后续可以开发续集、衍生剧、小说、游戏等多种形式的内容产品。

## 制作历程回顾

### 前期筹备（2024年3月-5月）

**剧本创作**：
项目的剧本创作历时两个月，编剧团队深入研究了重生题材的经典作品，同时结合当前商业环境的特点，创作出了既有娱乐性又有现实意义的故事。

**演员选角**：
选角工作持续了一个月，剧组从数百名候选演员中精心挑选，最终确定了现在的演员阵容。每一位演员都经过了严格的试镜和培训。

**场景勘察**：
为了确保拍摄效果，制作团队实地勘察了上海、深圳等多个城市的商务区，最终选定了最符合剧情需要的拍摄地点。

### 拍摄过程（2024年5月-8月）

**拍摄计划**：
整个拍摄过程严格按照预定计划执行，48集内容分为三个阶段完成，每个阶段都有明确的质量标准和时间节点。

**质量控制**：
拍摄过程中，剧投投平台派遣了专业的监制团队全程跟踪，确保每一个镜头都符合平台的质量要求。

**技术创新**：
项目在拍摄过程中采用了多项新技术，包括无人机航拍、稳定器拍摄等，为观众呈现更加精美的视觉效果。

### 后期制作（2024年8月-9月）

**剪辑工作**：
后期剪辑工作由业内知名剪辑师负责，通过精心的剪辑，确保每一集都有紧凑的节奏和完整的故事结构。

**特效制作**：
虽然是短剧项目，但在特效制作上毫不马虎。商战场面、都市景观等都通过特效技术得到了完美呈现。

**音效配乐**：
项目邀请了专业的音乐制作团队，为剧集量身定制了主题曲和背景音乐，进一步提升了观看体验。

## 市场预期

### 播放量预测

基于剧投投平台的数据分析模型，结合同类型项目的历史表现，预计《重生之商界女王》上线后的表现将十分亮眼：

**首周播放量**：预计达到2亿次
**首月播放量**：预计达到8亿次
**总播放量**：预计超过15亿次

### 收益预测

**付费用户**：预计吸引200万付费用户
**平均付费金额**：预计每用户30元
**总收益预期**：预计达到6000万元
**投资回报率**：预计达到923%

### 市场影响

**行业示范效应**：
作为剧投投平台的重点项目，《重生之商界女王》的成功将为整个短剧行业提供新的发展思路和制作标准。

**平台品牌提升**：
项目的成功将进一步提升剧投投平台在行业内的影响力和品牌价值，吸引更多优质项目和投资者。

**题材推广价值**：
该项目的成功将推动重生题材在短剧领域的进一步发展，为后续同类型项目提供参考。

## 营销策略

### 预热阶段（8月中旬-9月上旬）

**社交媒体预热**：
通过微博、抖音、小红书等社交媒体平台发布幕后花絮、演员访谈等内容，提前培养观众的期待感。

**KOL合作**：
与影视类、女性类KOL合作，通过他们的影响力扩大项目的知名度。

**媒体宣传**：
接受专业影视媒体的采访报道，提升项目的专业形象和权威性。

### 上线阶段（9月中旬）

**多平台联动**：
在各大短剧平台同步上线，通过平台资源的整合实现最大化的曝光效果。

**首发活动**：
在剧投投平台举办线上首发活动，邀请主创团队与观众互动，增强用户粘性。

**话题营销**：
围绕剧中的商战情节、女性励志等话题进行营销，引发观众的讨论和分享。

### 持续推广（9月下旬-10月）

**数据驱动优化**：
根据上线后的数据表现，及时调整营销策略，优化推广效果。

**口碑维护**：
通过优质的内容和良好的用户服务，维护项目的口碑和品牌形象。

**衍生内容开发**：
根据市场反响，适时推出衍生内容，延长项目的生命周期。

## 投资者回报

### 财务回报

基于保守估计，《重生之商界女王》项目的投资回报率将达到923%，这一数字远超传统影视投资的平均水平。

**投资成本**：650万元
**预期收益**：6000万元
**净利润**：5350万元
**投资回报率**：923%

### 战略价值

除了直接的财务回报外，该项目还将为投资者带来重要的战略价值：

**行业地位提升**：参与优质项目的投资将提升投资者在行业内的地位和影响力。

**经验积累**：通过参与项目的全过程，投资者将积累宝贵的短剧投资经验。

**网络资源扩展**：项目将为投资者提供接触更多行业资源的机会，扩展商业网络。

## 风险提示

### 市场风险

**竞争加剧**：短剧市场竞争日趋激烈，可能影响项目的市场表现。

**用户偏好变化**：观众喜好的快速变化可能对项目产生影响。

**平台政策调整**：各大平台政策的调整可能影响项目的分发和变现。

### 应对措施

**质量保证**：通过严格的质量控制确保内容的竞争力。

**灵活调整**：根据市场反馈及时调整营销和运营策略。

**多元化布局**：通过多平台、多渠道的布局降低单一风险。

## 未来规划

### 续集开发

如果《重生之商界女王》获得成功，剧投投平台计划开发续集，继续深挖这一IP的商业价值。

### IP扩展

**小说改编**：将剧集内容改编为网络小说，扩大IP影响力。

**游戏开发**：开发相关的手机游戏，实现IP的多元化变现。

**线下活动**：举办主题展览、粉丝见面会等线下活动，增强用户粘性。

### 经验总结

项目完成后，剧投投平台将对整个制作和运营过程进行全面总结，形成标准化的制作流程和质量标准，为后续项目提供参考。

## 结语

《重生之商界女王》项目的顺利完成，标志着剧投投平台在内容制作和项目管理方面又迈上了一个新台阶。我们相信，这部作品不仅能够为投资者带来丰厚的回报，也将为整个短剧行业的发展贡献力量。

面向未来，剧投投平台将继续坚持高质量、高标准的制作理念，为市场提供更多优质的短剧内容，为投资者创造更大的价值。我们期待《重生之商界女王》在9月的精彩亮相，也期待与更多合作伙伴携手，共同推动短剧行业的繁荣发展。

*项目详细信息和投资机会请关注剧投投官方平台，或联系我们的投资顾问团队。*
    `
  },
  {
    id: 5,
    title: "短剧出海正当时：中国短剧在东南亚市场表现亮眼",
    summary: "中国短剧正在加速出海步伐，在东南亚市场表现尤为突出。数据显示，中国短剧在泰国、越南、印尼等国的播放量同比增长超过300%。",
    category: "行业动态",
    author: "国际市场研究组",
    publishDate: "2024-08-05",
    readCount: 11200,
    coverImage: "https://via.placeholder.com/400x240/06B6D4/FFFFFF?text=出海报告",
    content: `
# 短剧出海正当时：中国短剧在东南亚市场表现亮眼

## 出海现状概览

随着中国短剧产业的快速发展和内容质量的不断提升，越来越多的优质短剧作品开始走向国际市场。据最新统计数据显示，2024年上半年，中国短剧在海外市场的总播放量达到50亿次，同比增长280%，其中东南亚市场贡献了超过60%的播放量。

这一亮眼表现不仅体现了中国短剧内容的国际竞争力，也为中国文化的海外传播开辟了新的渠道。短剧作为一种新兴的文化产品形态，正在成为中国文化软实力输出的重要载体。

## 东南亚市场表现分析

### 各国市场数据

**泰国市场**：
- 播放量：15.2亿次（同比增长320%）
- 用户数：850万人
- 平均观看时长：25分钟/天
- 最受欢迎题材：都市言情、古装剧

**越南市场**：
- 播放量：12.8亿次（同比增长295%）
- 用户数：720万人
- 平均观看时长：22分钟/天
- 最受欢迎题材：现代都市、校园青春

**印尼市场**：
- 播放量：10.5亿次（同比增长285%）
- 用户数：680万人
- 平均观看时长：20分钟/天
- 最受欢迎题材：家庭伦理、励志成长

**菲律宾市场**：
- 播放量：8.3亿次（同比增长270%）
- 用户数：520万人
- 平均观看时长：18分钟/天
- 最受欢迎题材：浪漫爱情、悬疑推理

**马来西亚市场**：
- 播放量：6.8亿次（同比增长260%）
- 用户数：420万人
- 平均观看时长：16分钟/天
- 最受欢迎题材：都市情感、商战职场

### 成功因素分析

**文化相近性**：
东南亚国家与中国在文化传统、价值观念等方面存在诸多相似之处，这为中国短剧在当地的传播提供了天然优势。特别是在家庭观念、情感表达等方面，中国短剧的内容很容易引起当地观众的共鸣。

**内容本土化**：
成功出海的中国短剧都注重内容的本土化改造，包括字幕翻译、文化背景适配、价值观调整等。这种本土化策略大大提升了内容的接受度和传播效果。

**平台合作**：
与当地主流视频平台的深度合作是中国短剧出海成功的关键因素。通过与本土平台的合作，中国短剧能够更好地触达目标用户群体。

**技术优势**：
中国在短视频制作技术、分发技术等方面的领先优势，为短剧出海提供了强有力的技术支撑。

## 出海模式创新

### 内容输出模式

**直接输出**：
将国内成功的短剧作品进行字幕翻译和文化适配后，直接在海外平台播出。这种模式成本较低，但需要注意文化差异的处理。

**本土化改编**：
基于国内成功的IP和剧本，在海外进行本土化改编制作。这种模式能够更好地适应当地市场需求，但投入成本相对较高。

**合拍模式**：
与海外制作团队合作，共同制作面向国际市场的短剧内容。这种模式能够充分利用双方的优势资源。

### 商业变现模式

**广告收入**：
通过在短剧中植入广告或在播放平台投放广告获得收入。这是目前最主要的变现方式。

**付费观看**：
在海外市场推广付费观看模式，虽然接受度还在培养中，但增长潜力巨大。

**IP授权**：
将成功的短剧IP授权给海外合作伙伴，开发衍生产品和服务。

**品牌合作**：
与海外品牌进行深度合作，通过内容营销实现商业价值。

## 成功案例分析

### 《都市情缘》海外版

**项目概况**：
《都市情缘》作为剧投投平台的明星项目，在国内取得巨大成功后，迅速启动了海外版本的制作。

**本土化策略**：
- 剧情适配：根据东南亚文化特点调整部分剧情设置
- 演员选择：启用当地知名演员参与演出
- 语言处理：提供多语种字幕和配音版本

**市场表现**：
- 总播放量：8.5亿次
- 覆盖国家：6个
- 用户反馈：4.8分（满分5分）
- 商业收益：1200万元

### 《霸道总裁的小娇妻》泰语版

**项目概况**：
该项目采用了完全本土化制作的模式，在泰国当地进行拍摄制作，但保留了原版的核心剧情和人物设定。

**创新亮点**：
- 全泰语对白，更贴近当地观众
- 融入泰国文化元素，增强代入感
- 启用泰国当红演员，提升关注度

**市场表现**：
- 泰国播放量：3.2亿次
- 用户增长：新增用户150万
- 社交传播：相关话题讨论量超过500万次
- 商业价值：带动相关产品销售增长40%

## 挑战与应对

### 主要挑战

**文化差异**：
不同国家和地区的文化背景、价值观念存在差异，需要在内容制作和推广过程中充分考虑。

**监管政策**：
各国对于外来文化产品的监管政策不同，需要深入了解并严格遵守当地法规。

**竞争加剧**：
随着中国短剧出海的增多，市场竞争也在加剧，需要不断提升内容质量和创新能力。

**技术适配**：
不同地区的网络环境、设备普及情况存在差异，需要进行相应的技术适配。

### 应对策略

**深度调研**：
在进入新市场前，进行深入的市场调研，了解当地的文化特点、用户喜好、竞争格局等。

**本土化团队**：
建立本土化的运营团队，包括内容策划、市场推广、用户服务等各个环节。

**合规管理**：
建立完善的合规管理体系，确保所有内容和运营活动都符合当地法规要求。

**技术优化**：
针对不同地区的技术环境进行优化，确保用户能够获得良好的观看体验。

## 未来发展趋势

### 市场扩展

**新兴市场开拓**：
除了东南亚市场外，中东、非洲、拉美等新兴市场也展现出巨大潜力，值得重点关注。

**发达市场突破**：
欧美等发达市场虽然进入门槛较高，但一旦成功突破，将带来更大的商业价值。

**细分市场深耕**：
针对不同国家和地区的特点，开发更加细分和专业化的内容产品。

### 技术创新

**AI技术应用**：
利用AI技术进行自动翻译、内容推荐、用户分析等，提升出海效率和效果。

**VR/AR技术**：
探索VR/AR等新技术在短剧内容中的应用，创造更加沉浸式的观看体验。

**5G技术普及**：
随着5G技术在全球的普及，短剧的传播和观看体验将得到进一步提升。

### 产业协同

**平台合作深化**：
与海外平台的合作将从简单的内容分发向深度的产业合作发展。

**产业链整合**：
整合制作、发行、营销等产业链各环节，形成更加完善的出海生态体系。

**标准化建设**：
推动行业标准化建设，为中国短剧出海提供更好的制度保障。

## 政策支持与建议

### 政策支持

**文化出口政策**：
国家对文化产品出口的政策支持为短剧出海提供了重要保障，包括税收优惠、资金扶持等。

**"一带一路"倡议**：
"一带一路"倡议为中国文化产品在沿线国家的传播提供了重要机遇。

**双边文化协定**：
与各国签署的双边文化协定为短剧出海提供了制度性保障。

### 发展建议

**加强政策引导**：
建议政府部门出台更加具体的短剧出海支持政策，包括资金扶持、税收优惠、平台对接等。

**完善服务体系**：
建立完善的出海服务体系，为企业提供市场信息、法律咨询、技术支持等服务。

**推动行业合作**：
鼓励行业内企业加强合作，共同开拓海外市场，避免恶性竞争。

**人才培养**：
加强国际化人才的培养，为短剧出海提供人才保障。

## 投资机会分析

### 投资价值

**市场空间巨大**：
海外短剧市场还处于发展初期，存在巨大的增长空间和投资机会。

**政策环境良好**：
国家对文化出口的政策支持为投资提供了良好的外部环境。

**技术优势明显**：
中国在短视频技术方面的领先优势为出海提供了竞争优势。

**商业模式成熟**：
国内短剧的商业模式已经相对成熟，可以为海外市场提供参考。

### 投资方向

**内容制作**：
投资优质的短剧内容制作，特别是具有国际化潜力的项目。

**技术服务**：
投资短剧制作、分发、营销等技术服务提供商。

**平台建设**：
投资海外短剧平台的建设和运营。

**产业服务**：
投资为短剧出海提供服务的相关企业，如翻译公司、营销公司等。

### 风险提示

**政策风险**：
海外政策环境的变化可能对投资产生影响。

**文化风险**：
文化差异可能导致内容接受度不高。

**竞争风险**：
市场竞争加剧可能影响投资回报。

**汇率风险**：
汇率波动可能影响投资收益。

## 结语

中国短剧出海正迎来前所未有的发展机遇。随着内容质量的不断提升、技术实力的持续增强、政策环境的日益完善，中国短剧在国际市场上的竞争力将进一步提升。

对于投资者而言，短剧出海不仅是一个巨大的商业机会，也是参与中国文化走向世界的历史进程。剧投投平台将继续加大对出海项目的投资力度，为投资者提供更多优质的投资机会。

我们相信，在各方的共同努力下，中国短剧必将在国际舞台上绽放更加璀璨的光芒，为中华文化的海外传播贡献更大的力量。

*更多出海项目投资机会，请关注剧投投平台或咨询我们的专业投资顾问。*
    `
  },
  {
    id: 6,
    title: "AI技术赋能短剧制作：成本降低40%，效率提升300%",
    summary: "人工智能技术正在深刻改变短剧制作流程，从剧本创作到后期制作，AI工具的应用让制作成本降低40%，制作效率提升300%。",
    category: "行业动态",
    author: "技术创新研究院",
    publishDate: "2024-08-02",
    readCount: 13580,
    coverImage: "https://via.placeholder.com/400x240/EF4444/FFFFFF?text=AI技术",
    content: `
# AI技术赋能短剧制作：成本降低40%，效率提升300%

## 技术革命的序幕

人工智能技术正在以前所未有的速度改变着各行各业，短剧制作领域也不例外。据剧投投技术创新研究院最新发布的《AI技术在短剧制作中的应用报告》显示，AI技术的广泛应用已经让短剧制作成本平均降低40%，制作效率提升300%，这一革命性的变化正在重塑整个短剧产业的生产模式。

从剧本创作的智能辅助，到拍摄过程的自动化处理，再到后期制作的智能化流程，AI技术正在短剧制作的每一个环节发挥着重要作用。这不仅大大降低了制作门槛，也为创作者提供了更多的创新可能性。

## AI技术应用全景

### 剧本创作阶段

**智能剧本生成**：
基于深度学习的AI系统能够分析大量成功剧本的结构和特点，为创作者提供剧本框架和情节建议。目前，AI辅助创作的剧本在逻辑性和商业价值方面已经达到了相当高的水准。

**角色设定优化**：
AI系统可以根据目标受众的喜好数据，为剧本中的角色提供最优的性格设定和人物关系设计，大大提高了角色的市场接受度。

**对话生成**：
自然语言处理技术的进步使得AI能够生成更加自然、符合人物性格的对话内容，为编剧提供了有力的创作支持。

**市场预测**：
通过分析历史数据和市场趋势，AI系统能够预测不同类型剧本的市场表现，为投资决策提供科学依据。

### 前期筹备阶段

**选角辅助**：
AI面部识别和情感分析技术能够帮助导演更准确地评估演员的表演能力和角色适配度，大大提高选角的效率和准确性。

**场景设计**：
基于AI的场景生成技术能够根据剧本需求自动生成场景设计方案，为美术指导提供创意灵感和技术支持。

**拍摄计划优化**：
AI算法能够根据演员档期、场地可用性、天气条件等多种因素，自动生成最优的拍摄计划，最大化资源利用效率。

**预算管理**：
智能预算管理系统能够实时监控制作成本，预警超支风险，帮助制片人更好地控制项目预算。

### 拍摄制作阶段

**智能摄影**：
AI驱动的摄影设备能够自动调整拍摄参数，确保画面质量的一致性。同时，智能跟焦、自动构图等功能大大降低了对专业摄影师的依赖。

**实时监控**：
AI系统能够实时分析拍摄画面，及时发现技术问题或表演瑕疵，避免后期重拍的成本损失。

**声音处理**：
智能音频处理技术能够实时降噪、调音，确保录音质量，减少后期音频处理的工作量。

**数据管理**：
AI系统能够自动整理和标记拍摄素材，为后期制作提供高效的素材管理服务。

### 后期制作阶段

**智能剪辑**：
这是AI技术应用最为成熟的领域之一。AI剪辑系统能够根据剧本和导演意图，自动完成粗剪工作，大大缩短了后期制作周期。

**特效制作**：
AI特效技术能够自动生成各种视觉效果，从简单的背景替换到复杂的动作场面，都能够通过AI技术快速完成。

**色彩校正**：
智能色彩校正系统能够自动分析画面色彩，进行统一的色调调整，确保整部作品的视觉一致性。

**音效合成**：
AI音效系统能够根据画面内容自动生成相应的音效，大大丰富了作品的听觉体验。

## 成本效益分析

### 成本降低详解

**人力成本节省**：
AI技术的应用使得许多原本需要专业人员完成的工作可以通过自动化系统完成，人力成本平均节省35%。

**时间成本压缩**：
制作周期的大幅缩短直接降低了项目的时间成本，平均节省30%的制作时间。

**设备成本优化**：
AI技术使得一些昂贵的专业设备可以被智能化的普通设备替代，设备成本降低25%。

**试错成本减少**：
AI的预测和分析能力大大减少了制作过程中的试错成本，避免了大量的重复工作。

### 效率提升分析

**制作流程优化**：
AI技术实现了制作流程的高度自动化，原本需要数周完成的工作现在可能只需要几天。

**质量控制提升**：
AI系统的实时监控和分析能力确保了制作质量的稳定性，减少了因质量问题导致的返工。

**资源配置优化**：
智能资源管理系统能够最大化利用现有资源，避免资源浪费和闲置。

**决策支持增强**：
AI提供的数据分析和预测能力为制作团队的决策提供了强有力的支持，提高了决策的准确性和效率。

## 技术应用案例

### 案例一：《AI制作的爱情故事》

**项目概况**：
这是剧投投平台首个完全采用AI技术制作的短剧项目，从剧本创作到后期制作，全程使用AI工具辅助。

**技术应用**：
- 剧本：AI生成初稿，人工精修
- 选角：AI分析演员适配度
- 拍摄：智能摄影设备全程跟拍
- 后期：AI自动剪辑和特效制作

**效果对比**：
- 制作周期：从传统的3个月缩短到1个月
- 制作成本：从500万降低到300万
- 质量水准：达到传统制作的95%水平
- 市场表现：播放量超过5亿次，投资回报率达到280%

### 案例二：《智能商战》

**项目概况**：
该项目在商战场面的制作中大量使用了AI特效技术，实现了以往需要大量资金才能完成的视觉效果。

**技术创新**：
- 虚拟场景：AI生成的商务场景
- 人群特效：AI合成的会议和活动场面
- 数据可视化：AI生成的商业图表和数据展示
- 音效设计：AI合成的环境音效

**成果展示**：
- 特效成本节省：70%
- 制作时间缩短：50%
- 视觉效果评分：4.7/5.0
- 观众满意度：92%

### 案例三：《未来校园》

**项目概况**：
这是一部科幻题材的校园短剧，大量使用了AI技术来创造未来感的视觉效果。

**技术亮点**：
- 场景设计：AI生成的未来校园场景
- 服装道具：AI辅助设计的未来感服装
- 特效制作：AI制作的科技感特效
- 音乐创作：AI辅助创作的背景音乐

**市场反响**：
- 创新性评价：业内首创
- 技术水准：达到电影级别
- 商业价值：授权收入超过200万
- 行业影响：成为AI制作的标杆案例

## 技术发展趋势

### 短期发展（1-2年）

**技术成熟度提升**：
现有AI技术将进一步成熟，应用门槛继续降低，更多制作团队将能够使用AI工具。

**工具集成化**：
各种AI工具将实现更好的集成，形成完整的AI制作工具链。

**成本进一步降低**：
随着技术的普及和竞争的加剧，AI工具的使用成本将进一步降低。

**质量标准提升**：
AI制作的内容质量将接近甚至超越传统制作方式。

### 中期发展（3-5年）

**全流程自动化**：
从剧本创作到成片输出的全流程自动化将基本实现。

**个性化定制**：
AI将能够根据不同观众群体的喜好，自动生成个性化的内容版本。

**实时制作**：
基于AI的实时制作技术将使得短剧制作变得像直播一样快速。

**跨媒体融合**：
AI技术将推动短剧与游戏、VR等其他媒体形式的深度融合。

### 长期展望（5-10年）

**完全智能化**：
AI将能够独立完成高质量短剧的制作，人类的角色将转向创意指导和质量监督。

**情感智能**：
AI将具备更强的情感理解和表达能力，制作出更具感染力的内容。

**虚拟演员**：
完全由AI生成的虚拟演员将成为可能，进一步降低制作成本。

**智能分发**：
AI将实现内容的智能分发，根据观众喜好自动推送最合适的内容。

## 行业影响分析

### 对制作方的影响

**门槛降低**：
AI技术大大降低了短剧制作的技术门槛和资金门槛，更多的创作者能够参与到内容制作中来。

**创作自由度提升**：
AI工具为创作者提供了更多的创作可能性，不再受限于技术和资金约束。

**竞争格局变化**：
技术实力将成为制作方竞争的新维度，掌握先进AI技术的团队将获得竞争优势。

**商业模式创新**：
AI技术催生了新的商业模式，如AI制作服务、技术授权等。

### 对投资方的影响

**投资回报提升**：
制作成本的降低和效率的提升直接提高了投资回报率。

**风险控制改善**：
AI的预测和分析能力帮助投资方更好地控制投资风险。

**投资决策优化**：
基于AI的数据分析为投资决策提供了更科学的依据。

**新投资机会**：
AI技术本身也成为了新的投资方向，技术服务商获得了更多投资机会。

### 对观众的影响

**内容丰富度提升**：
AI技术使得更多优质内容能够以较低成本制作出来，观众的选择更加丰富。

**观看体验改善**：
AI技术提升了内容的制作质量，为观众提供了更好的观看体验。

**个性化服务**：
AI能够根据观众喜好提供个性化的内容推荐和定制服务。

**互动性增强**：
AI技术为内容的互动性提供了更多可能，观众能够更深度地参与到内容中来。

## 挑战与应对

### 技术挑战

**技术标准化**：
目前AI技术在短剧制作中的应用还缺乏统一的标准，需要行业共同努力建立标准体系。

**技术可靠性**：
AI技术的稳定性和可靠性还需要进一步提升，特别是在关键制作环节的应用。

**技术更新速度**：
AI技术发展迅速，制作团队需要不断学习和适应新技术。

**数据安全**：
AI系统处理的大量数据涉及商业机密和个人隐私，需要建立完善的数据安全保护机制。

### 人才挑战

**复合型人才稀缺**：
既懂传统制作又了解AI技术的复合型人才非常稀缺，成为制约行业发展的重要因素。

**技能转型需求**：
传统制作人员需要学习新技术，适应新的制作模式。

**培训体系建设**：
需要建立完善的AI技术培训体系，为行业培养更多专业人才。

**人才竞争加剧**：
优秀的AI技术人才成为各大公司争夺的焦点，人才成本上升。

### 应对策略

**技术投入**：
加大对AI技术研发的投入，提升技术实力和竞争优势。

**人才培养**：
建立完善的人才培养体系，通过内部培训和外部合作培养专业人才。

**标准制定**：
积极参与行业标准的制定，推动技术标准化发展。

**生态建设**：
构建完善的AI技术生态体系，促进技术的共享和发展。

## 投资机会与建议

### 投资机会

**AI技术服务商**：
为短剧制作提供AI技术服务的公司具有巨大的投资价值。

**AI工具开发**：
开发专门针对短剧制作的AI工具和平台。

**技术培训服务**：
为行业提供AI技术培训和咨询服务。

**内容制作公司**：
掌握先进AI技术的内容制作公司将获得更大的市场份额。

### 投资建议

**关注技术领先性**：
优先投资技术实力强、创新能力突出的企业。

**重视应用场景**：
关注AI技术在具体应用场景中的效果和价值。

**考虑生态价值**：
投资能够构建完整生态体系的平台型企业。

**注意风险控制**：
充分评估技术风险和市场风险，做好风险控制。

## 结语

AI技术正在深刻改变短剧制作行业，这不仅是一场技术革命，更是一次产业升级的重要机遇。对于制作方而言，拥抱AI技术意味着更低的成本、更高的效率和更大的创作自由度。对于投资方而言，AI技术的应用将带来更高的投资回报和更好的风险控制。

剧投投平台将继续加大对AI技术的投入和应用，为平台上的创作者和投资者提供最先进的技术支持和服务。我们相信，在AI技术的推动下，短剧行业将迎来更加光明的未来，为观众提供更多优质的内容，为投资者创造更大的价值。

未来已来，让我们共同拥抱AI技术带来的无限可能，共同推动短剧行业的创新发展。

*了解更多AI技术在短剧制作中的应用，请关注剧投投技术创新研究院的最新报告。*
    `
  }
])

// 筛选后的新闻数据
const filteredNews = computed(() => {
  if (activeNewsCategory.value === "全部") {
    return newsData.value
  }
  return newsData.value.filter(news => news.category === activeNewsCategory.value)
})

// 加载短剧数据
const loadDramaData = async () => {
  loading.value = true
  error.value = null

  try {
    const response = await getPublicDramas({
      page: 1,
      pageSize: 12,
      status: 'funding' as any // 只获取募资中的短剧（剩余天数>0）
    })

    if (response.data && response.data.success) {
      dramas.value = response.data.data.list
    } else {
      error.value = '获取短剧数据失败'
    }
  } catch (err) {
    console.error('加载短剧数据失败:', err)
    error.value = '加载短剧数据失败，请稍后重试'
  } finally {
    loading.value = false
  }
}

// 加载网站统计数据
const loadWebsiteStats = async () => {
  statsLoading.value = true
  statsError.value = null

  try {
    const response = await getWebsiteStats()

    if (response && response.success && response.data) {
      websiteStats.value = response.data
    } else {
      statsError.value = response?.message || '获取统计数据失败'
    }
  } catch (err) {
    console.error('加载统计数据失败:', err)
    statsError.value = '加载统计数据失败，请稍后重试'
  } finally {
    statsLoading.value = false
  }
}

// 启动实时投资人数变化
const startRealtimeInvestors = () => {
  investorTimer = setInterval(() => {
    // 在1575-1685之间随机变化，使用更自然的变化幅度
    const currentValue = realtimeInvestors.value
    const min = 1575
    const max = 1685

    // 生成相对当前值的小幅变化，使变化更自然
    const changeRange = 10 // 每次变化范围
    const minChange = Math.max(min, currentValue - changeRange)
    const maxChange = Math.min(max, currentValue + changeRange)

    realtimeInvestors.value = Math.floor(Math.random() * (maxChange - minChange + 1)) + minChange
  }, 4000) // 每4秒变化一次
}

// 启动用户轮播
const startUserRotation = () => {
  userTimer = setInterval(() => {
    if (websiteStats.value?.users && websiteStats.value.users.length > 0) {
      currentUserIndex.value = (currentUserIndex.value + 1) % websiteStats.value.users.length
    }
  }, 3000) // 每3秒切换一次用户
}







// 计算属性
const currentUser = computed(() => {
  if (websiteStats.value?.users && websiteStats.value.users.length > 0) {
    return websiteStats.value.users[currentUserIndex.value]
  }
  return null
})

// 计算进度百分比
const calculateProgress = (current: number, goal: number): number => {
  return Math.min(Math.round((current / goal) * 100), 100);
};

// 格式化货币
const formatCurrency = (value: number): string => {
  return (value / 10000).toFixed(0) + ' 万';
};

// 在首页只显示最多12个项目（2行，每行6个）
const displayedProjects = computed(() => {
  return dramas.value.slice(0, 12);
});

// 清理定时器
const clearTimers = () => {
  if (investorTimer) {
    clearInterval(investorTimer)
    investorTimer = null
  }
  if (userTimer) {
    clearInterval(userTimer)
    userTimer = null
  }
}



// 在组件挂载时加载数据
onMounted(() => {
  loadTags() // 加载标签数据
  loadDramaData()
  loadWebsiteStats()
  loadPlatforms() // 加载短剧平台数据
  startRealtimeInvestors()
  startUserRotation()
})

// 组件卸载时清理定时器
onUnmounted(() => {
  clearTimers()
})

// 导航到项目详情页
const navigateToProjectDetail = (projectId: number) => {
  router.push(`/project/${projectId}`);
};

// 处理投资点击
const handleInvestment = (project: Drama) => {
  // 跳转到项目详情页面的投资部分
  router.push(`/project/${project.id}#investment`);
};

// 新闻分类切换
const changeNewsCategory = (category: string) => {
  activeNewsCategory.value = category;
};

// 导航到新闻详情页
const navigateToNewsDetail = (newsId: number) => {
  router.push(`/news/${newsId}`);
};

// 判断是否为最近的新闻（3天内）
const isRecentNews = (publishDate: string) => {
  const newsDate = new Date(publishDate);
  const now = new Date();
  const diffTime = Math.abs(now.getTime() - newsDate.getTime());
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
  return diffDays <= 3;
};

// 生成装饰性标签
const generateDecorativeTags = (title: string, category: string) => {
  const keywords = {
    '短剧市场': ['市场增长', '500亿规模', '爆发式增长'],
    '政策利好': ['规范化发展', '政策支持', '行业机遇'],
    '投资报告': ['180%回报', 'Q2业绩', '投资机会'],
    '项目更新': ['拍摄完成', '9月上线', '商界女王'],
    '出海': ['东南亚市场', '300%增长', '国际化'],
    'AI技术': ['成本降低40%', '效率提升300%', '技术革命']
  };

  const colors = [
    'bg-blue-500', 'bg-green-500', 'bg-purple-500', 'bg-orange-500',
    'bg-pink-500', 'bg-indigo-500', 'bg-teal-500', 'bg-red-500'
  ];

  // 根据标题关键词匹配标签
  let tags = [];
  for (const [key, values] of Object.entries(keywords)) {
    if (title.includes(key)) {
      tags = values;
      break;
    }
  }

  // 如果没有匹配到，使用分类相关标签
  if (tags.length === 0) {
    switch (category) {
      case '行业动态':
        tags = ['行业趋势', '市场分析', '发展前景'];
        break;
      case '投资资讯':
        tags = ['投资机会', '收益分析', '风险评估'];
        break;
      case '项目更新':
        tags = ['项目进展', '制作动态', '上线计划'];
        break;
      case '政策法规':
        tags = ['政策解读', '合规要求', '行业规范'];
        break;
      default:
        tags = ['热门', '推荐', '精选'];
    }
  }

  // 随机选择颜色并返回标签
  return tags.slice(0, 3).map((tag, index) => ({
    text: tag,
    color: colors[Math.floor(Math.random() * colors.length)]
  }));
};


</script>

<template>
  <div class="home">
    <!-- 轮播图 -->
    <HomeBanner />

    <!-- 指标看板 -->
    <section class="py-8 bg-gradient-to-r from-blue-50 to-purple-50">
      <div class="container mx-auto px-4">
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
          <!-- 已募资金总额 -->
          <div class="bg-white rounded-xl shadow-md p-6 flex items-center transform transition-all hover:scale-105 hover:shadow-lg">
            <div class="flex-shrink-0 bg-gradient-primary rounded-full p-4 mr-4">
              <svg class="w-8 h-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div>
              <div class="flex items-center">
                <h3 class="text-lg font-medium text-gray-500">已募资金总额</h3>
                <div class="ml-2 flex items-center text-green-500">
                  <svg class="w-4 h-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                  </svg>
                  <span class="text-xs ml-1">{{ websiteStats?.fundingGrowthRate || 36.4 }}%</span>
                </div>
              </div>
              <div class="text-3xl font-bold text-gray-800 mt-1">
                {{ websiteStats?.totalRaisedAmount?.toFixed(2) || '0.00' }} 万元
              </div>
              <div class="text-sm text-gray-500 mt-1">募资金额增长 <span class="text-primary font-medium">{{ websiteStats?.fundingGrowthRate || 36.4 }}%</span></div>
            </div>
          </div>
          
          <!-- 今日投资总额 -->
          <div class="bg-white rounded-xl shadow-md p-6 flex items-center transform transition-all hover:scale-105 hover:shadow-lg">
            <div class="flex-shrink-0 bg-gradient-primary rounded-full p-4 mr-4">
              <svg class="w-8 h-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            </div>
            <div>
              <h3 class="text-lg font-medium text-gray-500">今日投资总额</h3>
              <div class="text-2xl font-bold text-gray-800 mt-1">
                {{ websiteStats?.todayInvestmentAmount?.toFixed(2) || '0.00' }} 万 /
                {{ websiteStats?.historicalInvestmentTotal?.toFixed(0) || '0' }} 万
              </div>
              <div class="text-sm text-gray-500 mt-1">
                占比 <span class="text-primary font-medium">{{ websiteStats?.todayInvestmentRatio || 0 }}</span>
              </div>
            </div>
          </div>
          
          <!-- 实时投资人 -->
          <div class="bg-white rounded-xl shadow-md p-6 flex items-center transform transition-all hover:scale-105 hover:shadow-lg">
            <div class="flex-shrink-0 bg-gradient-primary rounded-full p-4 mr-4">
              <svg class="w-8 h-8 text-white" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
            </div>
            <div>
              <h3 class="text-lg font-medium text-gray-500">实时投资人</h3>
              <div class="text-3xl font-bold text-gray-800 mt-1">
                {{ realtimeInvestors }} <span class="text-sm font-normal">位</span>
              </div>
              <div class="text-sm text-gray-500 mt-1">
                <span class="bg-blue-100 text-blue-800 text-xs px-2 py-0.5 rounded">实时在线</span>
                <span v-if="currentUser" class="ml-2 text-xs">
                  {{ currentUser.username }} <span class="text-primary">{{ currentUser.userType }}</span>
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 投资亮点 -->
    <section class="py-16 bg-gray-50">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl md:text-4xl font-bold text-gradient">投资亮点</h2>
          <p class="text-xl text-gray-600 mt-4 max-w-3xl mx-auto">
            专业平台资源优势，完善投资保障体系，为投资人提供安全可靠的高收益投资机会
          </p>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          <div 
            v-for="item in highlights" 
            :key="item.id" 
            class="card hover:shadow-lg transition-shadow duration-300 overflow-hidden"
          >
            <div :class="['w-16 h-16 rounded-full flex items-center justify-center text-white mb-6', item.color]">
              <svg v-if="item.icon === 'chart-pie'" class="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 3.055A9.001 9.001 0 1020.945 13H11V3.055z" />
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20.488 9H15V3.512A9.025 9.025 0 0120.488 9z" />
              </svg>
              <svg v-else-if="item.icon === 'users'" class="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
              </svg>
              <svg v-else-if="item.icon === 'shield-check'" class="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
              <svg v-else-if="item.icon === 'trending-up'" class="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
              </svg>
            </div>
            
            <h3 class="text-xl font-semibold mb-2">{{ item.title }}</h3>
            <p class="text-gray-600 mb-4">{{ item.description }}</p>
            
            <div class="mt-auto pt-4 border-t border-gray-100">
              <p class="text-2xl font-bold text-primary">{{ item.roi }}</p>
            </div>
          </div>
        </div>
        
        <div class="text-center mt-12">
          <RouterLink to="/investment" class="btn btn-primary px-8 py-3 text-lg">
            查看投资方案
          </RouterLink>
        </div>
      </div>
    </section>
    
    <!-- 募资中短剧展示区 -->
    <section class="py-16 bg-white">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl md:text-4xl font-bold text-gradient">募资中短剧</h2>
          <p class="text-xl text-gray-600 mt-4 max-w-3xl mx-auto">
            精选优质短剧IP项目，打造爆款内容，共享行业红利
          </p>
        </div>
        
        <!-- 加载中状态 -->
        <div v-if="loading" class="flex justify-center items-center py-12">
          <div class="w-12 h-12 border-4 border-gray-200 border-t-primary rounded-full animate-spin"></div>
        </div>
        
        <!-- 错误提示 -->
        <div v-else-if="error" class="text-center py-12">
          <svg class="w-16 h-16 mx-auto text-red-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-3L13.732 4c-.77-1.333-2.694-1.333-3.464 0L3.34 16c-.77 1.333.192 3 1.732 3z" />
          </svg>
          <p class="mt-4 text-lg text-red-600">{{ error }}</p>
          <button 
            @click="loadDramaData" 
            class="mt-4 px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark transition-colors"
          >
            重试
          </button>
        </div>
        
        <!-- 无数据提示 -->
        <div v-else-if="dramas.length === 0" class="text-center py-12">
          <svg class="w-16 h-16 mx-auto text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2.586a1 1 0 00-.707.293l-2.414 2.414a1 1 0 01-.707.293h-3.172a1 1 0 01-.707-.293l-2.414-2.414A1 1 0 006.586 13H4" />
          </svg>
          <p class="mt-4 text-lg text-gray-600">暂无募资中的短剧项目</p>
        </div>
        
        <!-- 响应式网格布局 - 限制只显示12个项目（2行x6列） -->
        <div v-else class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-x-4 gap-y-8">
          <!-- 项目卡片 -->
          <div
            v-for="project in displayedProjects"
            :key="project.id"
            class="bg-white rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-xl hover:-translate-y-1 relative group border border-gray-100 hover:border-gray-200 cursor-pointer flex flex-col h-full"
            @click="navigateToProjectDetail(project.id)"
          >
            <!-- 封面图区域 (3:4比例) -->
            <div class="relative pt-[133.33%] bg-gray-100 overflow-hidden">
              <!-- 封面图 -->
              <div class="absolute inset-0 flex items-center justify-center transition-transform duration-500 group-hover:scale-105">
                <img
                  v-if="project.cover"
                  :src="project.cover"
                  :alt="project.title"
                  class="w-full h-full object-cover"
                />
                <div v-else class="w-full h-full flex items-center justify-center bg-purple-100">
                  <svg class="w-20 h-20 md:w-24 md:h-24 lg:w-20 lg:h-20 text-primary" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12,17.27L18.18,21l-1.64-7.03L22,9.24l-7.19-0.61L12,2L9.19,8.63L2,9.24l5.46,4.73L5.82,21L12,17.27z" />
                  </svg>
                </div>
              </div>
              
              <!-- 剩余天数角标 -->
              <div class="absolute top-2 right-2 bg-red-500 text-white px-2 py-1 text-xs md:text-sm lg:text-xs font-bold rounded-md shadow-md">
                剩余 {{ project.remainingDays }} 天
              </div>
            </div>
            
            <!-- 内容区域 -->
            <div class="p-3 md:p-4 lg:p-3 flex-1 flex flex-col">
              <!-- 标题 -->
              <h3 class="text-sm md:text-base lg:text-sm font-bold mb-2 h-6 md:h-7 lg:h-6 hover:text-primary transition-colors overflow-hidden">
                <div class="whitespace-nowrap animate-scroll-if-overflow">
                  {{ project.title }}
                </div>
              </h3>
              
              <!-- 类型标签 -->
              <div class="flex gap-1 mb-2 overflow-hidden">
                <DramaTag
                  v-for="tag in parseTagData(project.tags as any)"
                  :key="tag.id || tag.name"
                  :tag="tag"
                  class="text-[10px] md:text-xs lg:text-[10px] flex-shrink-0"
                />
              </div>
              
              <!-- 募资进度条 -->
              <div>
                <div class="flex justify-between text-xs md:text-sm lg:text-xs mb-1">
                  <span class="text-gray-600">已筹 {{ formatCurrency(project.currentFunding) }}</span>
                  <span class="font-medium">{{ calculateProgress(project.currentFunding, project.fundingGoal) }}%</span>
                </div>
                <div class="h-2 md:h-2.5 lg:h-2 bg-gray-200 rounded-full overflow-hidden">
                  <div 
                    class="h-full rounded-full bg-primary" 
                    :style="{
                      width: `${calculateProgress(project.currentFunding, project.fundingGoal)}%`
                    }"
                  ></div>
                </div>
                <div class="text-[10px] md:text-xs lg:text-[10px] text-gray-500 mt-1">
                  目标 {{ formatCurrency(project.fundingGoal) }}
                </div>
              </div>
            </div>
            
            <!-- 常驻按钮组 -->
            <div class="bg-white pt-2 px-3 pb-3 flex gap-2 mt-auto">
              <RouterLink
                :to="`/project/${project.id}`"
                class="flex-1 py-2 px-3 rounded-lg flex items-center justify-center bg-gray-100 hover:bg-gray-200 text-gray-800 text-center font-medium text-xs transition-colors"
                @click.stop
              >
                <svg class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                </svg>
                查看详情
              </RouterLink>
              <button
                @click="handleInvestment(project)"
                class="flex-1 py-2 px-3 rounded-lg flex items-center justify-center bg-primary hover:bg-primary-dark text-white text-center font-medium text-xs transition-colors"
              >
                <svg class="w-4 h-4 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v2a2 2 0 002 2z" />
                </svg>
                参与众筹
              </button>
            </div>
          </div>
        </div>
        
        <!-- 查看更多按钮 - 链接到短剧筹募页面 -->
        <div class="text-center mt-12">
          <RouterLink to="/projects" class="btn btn-primary px-8 py-3 text-lg">
            查看更多短剧
          </RouterLink>
        </div>
      </div>
    </section>
    
    <!-- 募资流程时间轴 -->
    <section class="py-16 bg-gradient-to-r from-blue-50 to-purple-50">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl md:text-4xl font-bold text-gradient">募资流程</h2>
          <p class="text-xl text-gray-600 mt-4 max-w-3xl mx-auto">
            为短剧制作方提供专业募资服务，从项目提交到资金到位，全程支持
          </p>
        </div>
        
        <!-- 桌面端横向时间轴 -->
        <div class="hidden md:block">
          <div class="relative">
            <!-- 流程步骤 -->
            <div class="grid grid-cols-4 gap-8 relative">
              <div 
                v-for="step in investmentProcess" 
                :key="step.id" 
                class="flex flex-col items-center text-center relative"
              >
                <!-- 序号圆圈 -->
                <div 
                  class="w-16 h-16 rounded-full bg-white border-4 border-primary flex items-center justify-center text-xl font-bold z-10 mb-6 relative"
                  @mouseenter="showTooltip(step.id)"
                  @mouseleave="hideTooltip()"
                >
                  {{ step.id }}
                  
                  <!-- 工具提示 -->
                  <div 
                    v-show="activeTooltip === step.id"
                    class="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 w-64 bg-white p-4 rounded-lg shadow-lg text-left z-20 text-sm"
                  >
                    <p class="text-gray-700">{{ step.details }}</p>
                    <div class="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-4 h-4 bg-white rotate-45"></div>
                  </div>
                </div>
                
                <!-- 图标 -->
                <div class="bg-gradient-primary w-12 h-12 rounded-full flex items-center justify-center text-white mb-4">
                  <svg v-if="step.icon === 'search'" class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                  </svg>
                  <svg v-else-if="step.icon === 'document-text'" class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                  </svg>
                  <svg v-else-if="step.icon === 'film'" class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4v16M17 4v16M3 8h4m10 0h4M3 12h18M3 16h4m10 0h4M4 20h16a1 1 0 001-1V5a1 1 0 00-1-1H4a1 1 0 00-1 1v14a1 1 0 001 1z" />
                  </svg>
                  <svg v-else-if="step.icon === 'cash'" class="w-6 h-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                  </svg>
                </div>
                
                <!-- 标题和描述 -->
                <h3 class="text-lg font-bold mb-2">{{ step.title }}</h3>
                <p class="text-gray-600 text-sm">{{ step.description }}</p>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 移动端纵向时间轴 -->
        <div class="md:hidden">
          <div class="relative pl-10">
            <!-- 流程步骤 -->
            <div class="space-y-8">
              <div 
                v-for="step in investmentProcess" 
                :key="step.id" 
                class="relative"
              >
                <!-- 序号圆圈 -->
                <div 
                  class="absolute left-0 top-0 transform -translate-x-1/2 w-10 h-10 rounded-full bg-white border-2 border-primary flex items-center justify-center text-lg font-bold z-10"
                  @click="activeTooltip === step.id ? hideTooltip() : showTooltip(step.id)"
                >
                  {{ step.id }}
                </div>
                
                <!-- 内容卡片 -->
                <div class="bg-white rounded-lg shadow-sm p-4">
                  <div class="flex items-center mb-3">
                    <!-- 图标 -->
                    <div class="bg-gradient-primary w-10 h-10 rounded-full flex items-center justify-center text-white mr-3 flex-shrink-0">
                      <svg v-if="step.icon === 'search'" class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                      </svg>
                      <svg v-else-if="step.icon === 'document-text'" class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                      <svg v-else-if="step.icon === 'film'" class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 4v16M17 4v16M3 8h4m10 0h4M3 12h18M3 16h4m10 0h4M4 20h16a1 1 0 001-1V5a1 1 0 00-1-1H4a1 1 0 00-1 1v14a1 1 0 001 1z" />
                      </svg>
                      <svg v-else-if="step.icon === 'cash'" class="w-5 h-5" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z" />
                      </svg>
                    </div>
                    
                    <!-- 标题 -->
                    <h3 class="text-lg font-bold">{{ step.title }}</h3>
                  </div>
                  
                  <!-- 描述 -->
                  <p class="text-gray-600 text-sm">{{ step.description }}</p>
                  
                  <!-- 详情（点击后展开） -->
                  <div v-if="activeTooltip === step.id" class="mt-3 text-sm text-gray-700 bg-gray-50 p-3 rounded">
                    {{ step.details }}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 募资注意事项 -->
        <div class="mt-16 p-4 bg-blue-50 rounded-lg border border-blue-200">
          <div class="flex items-start">
            <div class="flex-shrink-0 mr-3">
              <svg class="w-6 h-6 text-blue-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div>
              <h4 class="text-lg font-semibold text-blue-800 mb-2">募资注意事项</h4>
              <ul class="text-sm text-blue-700 space-y-2">
                <li>• 项目募资门槛：单个项目最低募资金额10万元起</li>
                <li>• 制作周期要求：标准制作周期3-6个月，需按时完成项目交付</li>
                <li>• 收益分配机制：项目收益按投资比例分配，制作方享有创作收益和版权收益</li>
                <li>• 平台服务费：平台收取募资金额6-12%的服务费，用于项目推广和管理服务</li>
                <li>• 合规要求：项目内容需符合国家相关法规，通过平台内容审核</li>
              </ul>
            </div>
          </div>
        </div>
        
        <!-- 项目发布按钮 -->
        <div class="text-center mt-8">
          <button @click="showContactModal = true" class="btn bg-primary hover:bg-primary-dark text-white px-8 py-3 rounded-lg transition-all">
            发布募资项目
          </button>
        </div>
      </div>
    </section>
    
    <!-- 募资人说 -->
    <section class="py-16 bg-gradient-to-br from-gray-50 to-white overflow-hidden">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl md:text-4xl font-bold text-gradient">募资人说</h2>
          <p class="text-xl text-gray-600 mt-4 max-w-3xl mx-auto">
            听听那些成功募资的创作者们的真实声音
          </p>
        </div>

        <!-- 两行水平滚动布局 -->
        <div class="relative max-w-7xl mx-auto space-y-8">
          <!-- 第一行滚动 -->
          <div class="overflow-hidden">
            <div class="flex animate-scroll-horizontal-1 gap-6">
              <div
                v-for="review in getFirstRowReviews()"
                :key="`row1-${review.id}`"
                class="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 backdrop-blur-sm flex-shrink-0 w-80"
              >
                <!-- 用户信息 -->
                <div class="flex items-center mb-4">
                  <!-- 头像显示首字母 -->
                  <div class="w-12 h-12 rounded-full bg-gradient-to-br from-blue-500 to-blue-600 flex items-center justify-center text-white font-bold text-lg flex-shrink-0">
                    {{ review.name.charAt(0) }}
                  </div>
                  <div class="ml-3">
                    <h4 class="font-semibold text-gray-900 text-base">{{ review.name }}</h4>
                    <p class="text-gray-600 text-sm">{{ review.company }}</p>
                  </div>
                </div>

                <!-- 评价内容 -->
                <div class="text-gray-700 leading-relaxed text-sm">
                  <p class="italic">"{{ review.review }}"</p>
                </div>

                <!-- 星级评分 -->
                <div class="flex items-center mt-4 pt-4 border-t border-gray-100">
                  <div class="flex text-sm">
                    <i
                      v-for="(star, index) in getStarDisplay(review.rating)"
                      :key="index"
                      class="fas fa-star"
                      :class="{
                        'text-yellow-400': star === 'full' || star === 'half',
                        'text-gray-300': star === 'empty'
                      }"
                    ></i>
                  </div>
                  <span class="ml-2 text-xs text-gray-500">{{ review.rating }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 第二行滚动 -->
          <div class="overflow-hidden hidden md:block">
            <div class="flex animate-scroll-horizontal-2 gap-6">
              <div
                v-for="review in getSecondRowReviews()"
                :key="`row2-${review.id}`"
                class="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 backdrop-blur-sm flex-shrink-0 w-80"
              >
                <!-- 用户信息 -->
                <div class="flex items-center mb-4">
                  <!-- 头像显示首字母 -->
                  <div class="w-12 h-12 rounded-full bg-gradient-to-br from-purple-500 to-purple-600 flex items-center justify-center text-white font-bold text-lg flex-shrink-0">
                    {{ review.name.charAt(0) }}
                  </div>
                  <div class="ml-3">
                    <h4 class="font-semibold text-gray-900 text-base">{{ review.name }}</h4>
                    <p class="text-gray-600 text-sm">{{ review.company }}</p>
                  </div>
                </div>

                <!-- 评价内容 -->
                <div class="text-gray-700 leading-relaxed text-sm">
                  <p class="italic">"{{ review.review }}"</p>
                </div>

                <!-- 星级评分 -->
                <div class="flex items-center mt-4 pt-4 border-t border-gray-100">
                  <div class="flex text-sm">
                    <i
                      v-for="(star, index) in getStarDisplay(review.rating)"
                      :key="index"
                      class="fas fa-star"
                      :class="{
                        'text-yellow-400': star === 'full' || star === 'half',
                        'text-gray-300': star === 'empty'
                      }"
                    ></i>
                  </div>
                  <span class="ml-2 text-xs text-gray-500">{{ review.rating }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
    
    <!-- 合作伙伴Logo墙 -->
    <section class="py-16">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl md:text-4xl font-bold text-gradient">合作伙伴</h2>
          <p class="text-xl text-gray-600 mt-4 max-w-3xl mx-auto">
            剧投投携手行业顶尖机构，共创短剧内容生态
          </p>
        </div>
        
        <!-- 短剧平台 -->
        <div class="mb-12">
          <!-- 加载状态 -->
          <div v-if="platformsLoading" class="text-center py-8">
            <div class="inline-flex items-center">
              <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-primary" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
              </svg>
              <span class="text-gray-600">加载平台数据中...</span>
            </div>
          </div>

          <!-- 错误状态 -->
          <div v-else-if="platformsError" class="text-center py-8">
            <div class="text-red-500 mb-4">{{ platformsError }}</div>
            <button
              @click="loadPlatforms"
              class="px-4 py-2 bg-primary text-white rounded-lg hover:bg-primary-dark transition-colors"
            >
              重新加载
            </button>
          </div>

          <!-- 平台列表 -->
          <div v-else-if="platforms.length > 0" class="logo-scroller overflow-hidden relative">
            <div class="scroller-inner flex animate-scroll items-center">
              <div
                v-for="platform in platforms"
                :key="`platform-${platform.id}`"
                class="logo-item flex-shrink-0 mx-1 transition-transform duration-300 hover:scale-110 cursor-pointer"
              >
                <div class="w-32 h-35 flex items-center justify-center rounded-xl p-2">
                  <img
                    v-if="platform.platform_logo_url"
                    :src="platform.platform_logo_url"
                    :alt="platform.platform_name"
                    class="max-w-full max-h-full object-contain rounded-lg"
                    @error="(e) => { (e.target as HTMLImageElement).style.display = 'none' }"
                  />
                  <span
                    class="text-sm font-bold text-gray-700"
                    :class="{ 'hidden': platform.platform_logo_url }"
                  >
                    {{ platform.platform_name }}
                  </span>
                </div>
              </div>
            </div>
          </div>

          <!-- 无数据状态 -->
          <div v-else class="text-center py-8">
            <div class="text-gray-500">暂无平台数据</div>
          </div>
        </div>
      </div>

    </section>
    
    <!-- 信任锚点横幅 -->
    <div class="fixed bottom-0 left-0 right-0 bg-white shadow-lg border-t border-gray-200 z-40">
      <div class="bg-gradient-to-r from-blue-50 to-purple-50 py-3 overflow-hidden">
        <div class="trust-scroll-container">
          <div class="trust-scroll-content">
            <!-- 信任标语 -->
            <div class="inline-flex items-center mx-6">
              <svg class="w-4 h-4 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
              <span class="text-sm text-gray-700">SSL 安全加密保护</span>
            </div>

            <div class="inline-flex items-center mx-6">
              <svg class="w-4 h-4 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z" />
              </svg>
              <span class="text-sm text-gray-700">资金银行托管</span>
            </div>

            <div class="inline-flex items-center mx-6">
              <svg class="w-4 h-4 text-green-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
              </svg>
              <span class="text-sm text-gray-700">收益定期结算</span>
            </div>

            <!-- 统计数据 -->
            <div class="inline-flex items-center mx-6">
              <svg class="w-4 h-4 text-primary mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
              <span class="text-sm text-gray-700">累计募资 </span>
              <span class="text-sm font-bold text-primary ml-1">{{ websiteStats?.totalRaisedAmount?.toFixed(0) || '21690' }} 万元</span>
            </div>

            <div class="inline-flex items-center mx-6">
              <svg class="w-4 h-4 text-primary mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
              </svg>
              <span class="text-sm text-gray-700">投资人数 </span>
              <span class="text-sm font-bold text-primary ml-1">{{ realtimeInvestors }} 位</span>
            </div>

            <!-- 真实投资记录 -->
            <div class="inline-flex items-center mx-6">
              <svg class="w-4 h-4 text-orange-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              <span class="text-sm text-gray-700">张** 刚刚投资 ¥100,000</span>
            </div>

            <div class="inline-flex items-center mx-6">
              <svg class="w-4 h-4 text-orange-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              <span class="text-sm text-gray-700">李** 2分钟前投资 ¥200,000</span>
            </div>

            <div class="inline-flex items-center mx-6">
              <svg class="w-4 h-4 text-orange-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              <span class="text-sm text-gray-700">王** 5分钟前投资 ¥150,000</span>
            </div>

            <div class="inline-flex items-center mx-6">
              <svg class="w-4 h-4 text-orange-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              <span class="text-sm text-gray-700">陈** 8分钟前投资 ¥300,000</span>
            </div>

            <div class="inline-flex items-center mx-6">
              <svg class="w-4 h-4 text-orange-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              <span class="text-sm text-gray-700">刘** 10分钟前投资 ¥250,000</span>
            </div>

            <div class="inline-flex items-center mx-6">
              <svg class="w-4 h-4 text-orange-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              <span class="text-sm text-gray-700">赵** 15分钟前投资 ¥180,000</span>
            </div>

            <div class="inline-flex items-center mx-6">
              <svg class="w-4 h-4 text-orange-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              <span class="text-sm text-gray-700">孙** 18分钟前投资 ¥120,000</span>
            </div>

            <div class="inline-flex items-center mx-6">
              <svg class="w-4 h-4 text-orange-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              <span class="text-sm text-gray-700">周** 20分钟前投资 ¥350,000</span>
            </div>

            <div class="inline-flex items-center mx-6">
              <svg class="w-4 h-4 text-orange-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              <span class="text-sm text-gray-700">吴** 25分钟前投资 ¥280,000</span>
            </div>

            <div class="inline-flex items-center mx-6">
              <svg class="w-4 h-4 text-orange-500 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13 10V3L4 14h7v7l9-11h-7z" />
              </svg>
              <span class="text-sm text-gray-700">郑** 30分钟前投资 ¥160,000</span>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 为底部固定条留出空间 -->
    <div class="h-[112px]"></div>
    
    <!-- 行业快讯 News Ticker -->
    <section class="py-16 bg-gray-50">
      <div class="container mx-auto px-4">
        <div class="text-center mb-12">
          <h2 class="text-3xl md:text-4xl font-bold text-gradient">行业快讯</h2>
          <p class="text-xl text-gray-600 mt-4 max-w-3xl mx-auto">
            实时掌握短剧行业动态，了解最新政策与市场趋势
          </p>
        </div>
        
        <div class="flex flex-col lg:flex-row gap-8">
          <!-- 左侧垂直标签导航 -->
          <div class="w-full lg:w-64 flex-shrink-0">
            <div class="bg-white rounded-xl shadow-md overflow-hidden">
              <div class="divide-y divide-gray-100">
                <button
                  v-for="(category, index) in newsCategories"
                  :key="index"
                  @click="changeNewsCategory(category)"
                  class="w-full py-4 px-6 text-left font-medium transition-colors relative flex items-center"
                  :class="activeNewsCategory === category ? 'text-primary bg-primary/5' : 'text-gray-700 hover:bg-gray-50'"
                >
                  <!-- 激活指示图标 -->
                  <svg v-if="activeNewsCategory === category" class="w-5 h-5 mr-2 text-primary" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7" />
                  </svg>
                  <span>{{ category }}</span>
                  
                  <!-- 激活指示条 -->
                  <div 
                    v-if="activeNewsCategory === category" 
                    class="absolute left-0 top-0 bottom-0 w-1 bg-primary"
                  ></div>
                </button>
              </div>
            </div>
            
            <!-- 查看全部按钮 - 已隐藏 -->
            <!-- <div class="mt-4">
              <RouterLink to="/news" class="w-full py-3 px-6 bg-white rounded-xl shadow-sm border border-gray-200 flex justify-center items-center font-medium text-gray-700 hover:bg-gray-50 transition-colors">
                <span>查看全部新闻</span>
                <svg class="w-4 h-4 ml-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M14 5l7 7m0 0l-7 7m7-7H3" />
                </svg>
              </RouterLink>
            </div> -->
          </div>
          
          <!-- 右侧新闻卡片网格 -->
          <div class="flex-grow">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              <!-- 新闻卡片容器 -->
              <div
                v-for="news in filteredNews.slice(0, 6)"
                :key="news.id"
                class="relative group"
              >
                <!-- 装饰性标签 -->
                <div class="absolute -top-2 -left-2 z-10 flex flex-wrap gap-1">
                  <div
                    v-for="(tag, index) in generateDecorativeTags(news.title, news.category)"
                    :key="index"
                    :class="tag.color"
                    class="text-white text-xs px-2 py-1 rounded-full font-medium shadow-lg transform rotate-[-5deg] group-hover:rotate-0 transition-transform duration-300"
                    :style="{
                      transform: `rotate(${-5 + index * 3}deg) translateY(${index * 8}px)`,
                      zIndex: 10 - index
                    }"
                  >
                    {{ tag.text }}
                  </div>
                </div>

                <!-- 右侧装饰标签 -->
                <div class="absolute -top-1 -right-1 z-10">
                  <div
                    :class="generateDecorativeTags(news.title, news.category)[0]?.color || 'bg-blue-500'"
                    class="text-white text-xs px-2 py-1 rounded-full font-medium shadow-lg transform rotate-[8deg] group-hover:rotate-[15deg] transition-transform duration-300"
                  >
                    {{ news.category }}
                  </div>
                </div>

                <!-- 新闻卡片 -->
                <div
                  class="bg-white rounded-xl shadow-md overflow-hidden transition-all duration-300 hover:shadow-lg hover:-translate-y-1 flex flex-col cursor-pointer relative"
                  @click="navigateToNewsDetail(news.id)"
                >
                <!-- 封面图 (16:9比例) -->
                <div class="relative pt-[56.25%] bg-gray-200 overflow-hidden">
                  <!-- 封面图 -->
                  <img
                    :src="news.coverImage"
                    :alt="news.title"
                    class="absolute inset-0 w-full h-full object-cover"
                    @error="(e) => { (e.target as HTMLImageElement).style.display = 'none' }"
                  />

                  <!-- 封面图加载失败时的占位 -->
                  <div class="absolute inset-0 flex items-center justify-center bg-gray-200">
                    <svg class="w-16 h-16 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M19 20H5a2 2 0 01-2-2V6a2 2 0 012-2h14a2 2 0 012 2v12a2 2 0 01-2 2z" />
                      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M16 2v4M8 2v4M3 10h18" />
                    </svg>
                  </div>

                  <!-- 分类标签 -->
                  <div class="absolute top-2 left-2 px-2 py-1 bg-primary text-white text-xs font-bold rounded">
                    {{ news.category }}
                  </div>

                  <!-- New标签 (最近3天的新闻) -->
                  <div v-if="isRecentNews(news.publishDate)" class="absolute top-2 right-2 bg-red-500 text-white text-xs font-bold px-2 py-1 rounded">
                    New
                  </div>
                </div>
                
                <!-- 内容区域 -->
                <div class="p-4 flex-grow flex flex-col">
                  <!-- 标题 -->
                  <h3 class="font-bold text-gray-800 mb-2 line-clamp-2 hover:text-primary hover:underline transition-colors">
                    {{ news.title }}
                  </h3>

                  <!-- 摘要 -->
                  <p class="text-sm text-gray-600 mb-4 line-clamp-2 flex-grow">
                    {{ news.summary }}
                  </p>

                  <!-- 底部信息 -->
                  <div class="flex justify-between items-center mt-auto pt-3 border-t border-gray-100">
                    <div class="flex items-center">
                      <span class="text-xs text-gray-500">{{ news.publishDate }}</span>
                      <span class="mx-2 text-gray-300">|</span>
                      <span class="text-xs text-gray-500">{{ news.author }}</span>
                      <span class="mx-2 text-gray-300">|</span>
                      <span class="text-xs text-gray-500 flex items-center">
                        <svg class="w-3 h-3 mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
                        </svg>
                        {{ news.readCount.toLocaleString() }}
                      </span>
                    </div>
                    
                    <!-- 分享按钮 (悬浮显示) -->
                    <div class="opacity-0 group-hover:opacity-100 transition-opacity">
                      <div class="flex space-x-2">
                        <button class="w-6 h-6 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors">
                          <svg class="w-3 h-3 text-gray-600" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M22.05 7.54a4.47 4.47 0 0 0-3.3-1.46 4.53 4.53 0 0 0-4.53 4.53c0 .35.04.7.08 1.05A12.9 12.9 0 0 1 5 6.89a4.3 4.3 0 0 0-.65 2.26c0 1.53.8 2.87 2 3.64a4.3 4.3 0 0 1-2.02-.57v.08a4.55 4.55 0 0 0 3.63 4.44c-.4.08-.8.13-1.21.13-.3 0-.56-.03-.87-.09a4.54 4.54 0 0 0 4.22 3.15 9.56 9.56 0 0 1-5.66 1.94c-.34 0-.7-.03-1.05-.08a13.36 13.36 0 0 0 7.04 2.04c8.08 0 12.52-6.7 12.52-12.52 0-.19-.01-.37-.01-.56a8.93 8.93 0 0 0 2.2-2.27c-.82.38-1.69.62-2.6.72a4.37 4.37 0 0 0 1.97-2.51z"></path>
                          </svg>
                        </button>
                        <button class="w-6 h-6 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-colors">
                          <svg class="w-3 h-3 text-gray-600" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M21.385 15.992a1.599 1.599 0 0 1-1.695.054 8.217 8.217 0 0 0-4.948-1.158 1.574 1.574 0 0 1-1.673-1.472A1.574 1.574 0 0 1 14.54 11.72a11.321 11.321 0 0 1 6.814 1.593 1.575 1.575 0 0 1 .031 2.68zm.358-4.997a.999.999 0 0 1-1.039.037 12.133 12.133 0 0 0-7.128-1.593 1 1 0 0 1-1.066-.932.998.998 0 0 1 .933-1.066 14.133 14.133 0 0 1 8.298 1.855 1 1 0 0 1 .002 1.699zm-9.53-7.562a15 15 0 0 0-9.5 1.727A1.001 1.001 0 0 0 2.34 6.67c.36 0 .72-.13.996-.379A13 13 0 0 1 12 4.557a13 13 0 0 1 8.663 1.734.994.994 0 0 0 1.38-.28 1 1 0 0 0-.279-1.38 15 15 0 0 0-9.5-1.727zm-3.846 9.707a2.999 2.999 0 1 0 5.998 0 2.999 2.999 0 0 0-5.998 0z"></path>
                          </svg>
                        </button>
                      </div>
                    </div>
                  </div>
                </div>
                <!-- 新闻卡片结束 -->
                </div>
                <!-- 新闻卡片容器结束 -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>

  </div>

  <!-- 项目专管员联系弹窗 -->
  <div v-if="showContactModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50" @click="showContactModal = false">
    <div class="bg-white rounded-lg p-8 max-w-md w-full mx-4" @click.stop>
      <div class="text-center">
        <h3 class="text-xl font-bold mb-4">联系项目专管员</h3>
        <p class="text-gray-600 mb-6">扫描下方二维码，添加项目专管员微信，获取专业的募资服务支持</p>

        <!-- 二维码区域 -->
        <div class="bg-gray-100 w-48 h-48 mx-auto mb-6 flex items-center justify-center rounded-lg">
          <div class="text-center">
            <svg class="w-16 h-16 mx-auto text-gray-400 mb-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
            </svg>
            <p class="text-sm text-gray-500">项目专管员二维码</p>
            <p class="text-xs text-gray-400 mt-1">微信号：juTouTou2024</p>
          </div>
        </div>

        <div class="text-sm text-gray-500 mb-6">
          <p>工作时间：周一至周五 9:00-18:00</p>
          <p>专业服务：项目评估、募资指导、资金管理</p>
        </div>

        <button @click="showContactModal = false" class="w-full bg-primary text-white py-3 rounded-lg hover:bg-primary-dark transition-colors">
          我知道了
        </button>
      </div>
    </div>
  </div>
</template>

<style scoped>
.btn-text-gradient {
  background: linear-gradient(to right, var(--color-purple-gradient-start), var(--color-purple-gradient-end));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  color: transparent; /* 使用标准color属性替代非标准的text-fill-color */
}

/* 环形进度条动画 */
@keyframes progress {
  0% {
    stroke-dasharray: 0 100;
  }
}

svg path:nth-child(2) {
  animation: progress 1.5s ease-out forwards;
}

/* Logo墙滚动动画 */
.hover\:scale-120:hover {
  transform: scale(1.2);
}

.logo-scroller {
  mask-image: linear-gradient(to right, transparent, black 5%, black 95%, transparent);
  display: flex;
  justify-content: center;
  align-items: center;
}

.scroller-inner {
  width: max-content;
  padding-block: 1rem;
  display: flex;
  align-items: center;
}

@keyframes scroll {
  from {
    transform: translateX(0);
  }
  to {
    transform: translateX(calc(-50% - 0.75rem));
  }
}

@keyframes scroll-reverse {
  from {
    transform: translateX(calc(-50% - 0.75rem));
  }
  to {
    transform: translateX(0);
  }
}

.animate-scroll {
  animation: scroll 30s linear infinite;
}

.animate-scroll-reverse {
  animation: scroll-reverse 30s linear infinite;
}

/* 暂停滚动 */
.logo-scroller:hover .scroller-inner {
  animation-play-state: paused;
}



/* 响应式设计 */
@media (max-width: 768px) {
  .fundraiser-review-card {
    width: 320px;
  }
}

/* 弹幕动画 */
.danmu-container {
  width: 100%;
  overflow: hidden;
}

.danmu-scroller {
  display: inline-block;
  white-space: nowrap;
  animation: danmu-scroll 30s linear infinite;
  padding: 2px 0;
}

@keyframes danmu-scroll {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

/* 移动端信任信息滚动 */
.trust-info-scroller {
  display: inline-block;
  white-space: nowrap;
  animation: trust-info-scroll 15s linear infinite;
  padding: 2px 0;
}

@keyframes trust-info-scroll {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-120%);
  }
}

/* 标题滚动动画 */
.animate-scroll-if-overflow {
  display: inline-block;
  animation: scroll-text 8s linear infinite;
  animation-play-state: paused;
}

/* 当文本溢出时启用滚动动画 */
h3:hover .animate-scroll-if-overflow {
  animation-play-state: running;
}

@keyframes scroll-text {
  0% {
    transform: translateX(0);
  }
  25% {
    transform: translateX(0);
  }
  75% {
    transform: translateX(calc(-100% + 100px));
  }
  100% {
    transform: translateX(calc(-100% + 100px));
  }
}

/* 信任锚点滚动 */
.trust-scroll-container {
  overflow: hidden;
  width: 100%;
}

.trust-scroll-content {
  display: flex;
  align-items: center;
  white-space: nowrap;
  animation: trust-scroll 30s linear infinite;
}

@keyframes trust-scroll {
  0% {
    transform: translateX(100%);
  }
  100% {
    transform: translateX(-100%);
  }
}

/* 悬停暂停滚动 */
.trust-scroll-container:hover .trust-scroll-content {
  animation-play-state: paused;
}

/* 募资人说两行水平滚动动画 */
.animate-scroll-horizontal-1 {
  animation: scroll-horizontal-1 10s linear infinite;
}

.animate-scroll-horizontal-2 {
  animation: scroll-horizontal-2 7.5s linear infinite;
}

@keyframes scroll-horizontal-1 {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

@keyframes scroll-horizontal-2 {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-50%);
  }
}

/* 悬停暂停动画 */
.animate-scroll-horizontal-1:hover,
.animate-scroll-horizontal-2:hover {
  animation-play-state: paused;
}

/* 自定义图标高度 */
.h-35 {
  height: 8.75rem; /* 35 * 0.25rem = 8.75rem */
}

/* 为底部栏留出空间 */
#app {
  padding-bottom: 112px;
}
</style>